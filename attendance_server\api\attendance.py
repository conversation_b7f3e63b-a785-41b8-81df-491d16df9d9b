"""
Attendance records API endpoints.
"""
from fastapi import APIRouter, Query, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from ..db.database import get_db
from ..db.crud import get_attendance_records
from ..schemas.attendance import AttendanceResponse

router = APIRouter()

@router.get('/attendance', response_model=List[AttendanceResponse])
async def list_attendance(
    device_id: Optional[str] = Query(None, description="Filter by device ID"),
    date: Optional[str] = Query(None, description="Filter by date (YYYY-MM-DD)"),
    user: Optional[str] = Query(None, description="Filter by user ID"),
    db: Session = Depends(get_db)
) -> List[AttendanceResponse]:
    """Get attendance records with optional filtering."""
    try:
        records = get_attendance_records(db, device_id, date, user)
        return [
            AttendanceResponse.model_validate({
                "id": record.id,
                "device_id": record.device_id,
                "user_id": record.user_id,
                "timestamp": record.timestamp,
                "record_type": record.record_type,
                "created_at": record.created_at
            })
            for record in records
        ]
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching attendance records: {str(e)}"
        )