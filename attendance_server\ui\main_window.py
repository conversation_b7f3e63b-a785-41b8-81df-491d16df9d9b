"""
Main window UI implementation.
"""
import sys
from datetime import datetime
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QTableWidget, QTableWidgetItem,
    QLabel, QStatusBar, QHeaderView
)
from PyQt6.QtCore import Qt, QTimer

from ..db.database import SessionLocal
from ..db.crud import get_all_devices, update_device_sync_status

class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("考勤系统服务端")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create device table
        self.create_device_table()
        layout.addWidget(self.device_table)
        
        # Create control buttons
        button_layout = QHBoxLayout()
        self.refresh_button = QPushButton("刷新设备状态")
        self.refresh_button.clicked.connect(self.refresh_devices)
        button_layout.addWidget(self.refresh_button)
        
        self.sync_button = QPushButton("请求同步")
        self.sync_button.clicked.connect(self.request_sync)
        self.sync_button.setEnabled(False)
        button_layout.addWidget(self.sync_button)
        
        layout.addLayout(button_layout)
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Setup auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_devices)
        self.refresh_timer.start(5000)  # Refresh every 5 seconds
        
        # Initial refresh
        self.refresh_devices()
    
    def create_device_table(self):
        """Create and configure the device table."""
        self.device_table = QTableWidget()
        self.device_table.setColumnCount(6)
        self.device_table.setHorizontalHeaderLabels([
            "设备ID", "名称", "分组", "状态", "同步状态", "最后心跳"
        ])
        
        # Configure table
        header = self.device_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        
        self.device_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.device_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.device_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
    
    def refresh_devices(self):
        """Refresh the device table with current data."""
        try:
            db = SessionLocal()
            devices = get_all_devices(db)
            
            self.device_table.setRowCount(len(devices))
            for row, device in enumerate(devices):
                self.device_table.setItem(row, 0, QTableWidgetItem(device.device_id))
                self.device_table.setItem(row, 1, QTableWidgetItem(device.name or device.device_id))
                self.device_table.setItem(row, 2, QTableWidgetItem(device.group or ""))
                
                status_item = QTableWidgetItem("在线" if device.is_online else "离线")
                status_item.setForeground(Qt.GlobalColor.green if device.is_online else Qt.GlobalColor.red)
                self.device_table.setItem(row, 3, status_item)
                
                self.device_table.setItem(row, 4, QTableWidgetItem(device.sync_status))
                
                last_heartbeat = (device.last_heartbeat.strftime("%Y-%m-%d %H:%M:%S")
                                if device.last_heartbeat else "从未")
                self.device_table.setItem(row, 5, QTableWidgetItem(last_heartbeat))
            
            self.status_bar.showMessage(f"上次更新: {datetime.now().strftime('%H:%M:%S')}")
        
        except Exception as e:
            self.status_bar.showMessage(f"刷新失败: {str(e)}")
        finally:
            db.close()
    
    def on_selection_changed(self):
        """Handle device selection change."""
        selected_rows = self.device_table.selectionModel().selectedRows()
        self.sync_button.setEnabled(len(selected_rows) > 0)
    
    def request_sync(self):
        """Request synchronization from selected device."""
        selected_rows = self.device_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        device_id = self.device_table.item(row, 0).text()
        
        try:
            db = SessionLocal()
            update_device_sync_status(db, device_id, 'pending')
            self.status_bar.showMessage(f"已请求设备 {device_id} 同步数据")
            self.refresh_devices()
        except Exception as e:
            self.status_bar.showMessage(f"请求同步失败: {str(e)}")
        finally:
            db.close() 