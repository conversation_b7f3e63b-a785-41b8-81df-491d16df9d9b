import sqlite3

def init_db(db_path='attendance_server.db'):
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    
    # 创建设备表
    cur.execute('''CREATE TABLE IF NOT EXISTS devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id TEXT UNIQUE,
        last_heartbeat DATETIME,
        online INTEGER,
        last_sync DATETIME,
        sync_status TEXT
    )''')

    # 创建考勤记录表
    cur.execute('''CREATE TABLE IF NOT EXISTS attendance_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id TEXT,
        sfz_name TEXT,
        sfz_id TEXT,
        sfz_group TEXT,
        enter_time DATETIME,
        md5sum TEXT
    )''')

    # 创建同步日志表
    cur.execute('''CREATE TABLE IF NOT EXISTS sync_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_id TEXT,
        sync_time DATETIME,
        file_name TEXT,
        status TEXT,
        detail TEXT
    )''')

    # 创建系统日志表
    cur.execute('''CREATE TABLE IF NOT EXISTS logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        time DATETIME DEFAULT CURRENT_TIMESTAMP,
        level TEXT,
        msg TEXT
    )''')

    conn.commit()
    conn.close()
    print("数据库初始化完成")

if __name__ == '__main__':
    init_db() 