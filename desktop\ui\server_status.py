"""
服务端状态显示组件
用于显示服务端运行状态、实时日志和系统信息
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit, 
    QPushButton, QComboBox, QGroupBox, QSplitter, QFrame,
    QScrollArea, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QTextCharFormat, QTextCursor, QPalette
from datetime import datetime, timezone, timedelta
from ..utils.time_utils import create_log_entry_timestamp
from typing import Dict, List, Optional
import json
import sys
from pathlib import Path

# 导入网络工具
try:
    # 尝试相对导入
    from ..utils.network_utils import get_local_ip_address, format_ip_display, get_all_local_ip_addresses
except ImportError:
    try:
        # 尝试绝对导入
        import socket
        def get_local_ip_address():
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                    s.connect(("*******", 80))
                    return s.getsockname()[0]
            except:
                return "127.0.0.1"

        def get_all_local_ip_addresses():
            try:
                hostname = socket.gethostname()
                addr_info = socket.getaddrinfo(hostname, None)
                ip_addresses = []
                for info in addr_info:
                    ip = info[4][0]
                    if '.' in ip and ip != '127.0.0.1':
                        if ip not in ip_addresses:
                            ip_addresses.append(ip)
                return ip_addresses
            except:
                return []

        def format_ip_display(ip):
            return f"当前IP地址: {ip or '获取失败'}"
    except ImportError:
        # 最后的备用函数
        def get_local_ip_address():
            return "127.0.0.1"
        def get_all_local_ip_addresses():
            return []
        def format_ip_display(ip):
            return f"当前IP地址: {ip or '获取失败'}"

class IPAddressDisplay(QWidget):
    """IP地址显示组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_ips = []
        self.init_ui()
        self.update_ip_address()

        # 设置定时器定期更新IP地址（每30秒检查一次）
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ip_address)
        self.update_timer.start(30000)  # 30秒

    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)

        # IP地址标签
        self.ip_label = QLabel("IP地址: 获取中...")
        self.ip_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 12px;
                font-weight: bold;
                padding: 2px 8px;
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
        """)

        layout.addWidget(self.ip_label)
        layout.addStretch()  # 添加弹性空间

    def update_ip_address(self):
        """更新IP地址显示"""
        try:
            new_ips = get_all_local_ip_addresses()
            if new_ips != self.current_ips:
                self.current_ips = new_ips

                # 格式化显示所有IP地址
                if new_ips:
                    display_text = f"IP地址: {', '.join(new_ips)}"
                else:
                    display_text = "IP地址: 获取失败"

                self.ip_label.setText(display_text)

                # 根据IP获取状态设置不同的样式
                if new_ips:
                    # 成功获取到有效IP
                    self.ip_label.setStyleSheet("""
                        QLabel {
                            color: #27ae60;
                            font-size: 12px;
                            font-weight: bold;
                            padding: 2px 8px;
                            background-color: #d5f4e6;
                            border: 1px solid #27ae60;
                            border-radius: 3px;
                        }
                    """)
                else:
                    # IP获取失败
                    self.ip_label.setStyleSheet("""
                        QLabel {
                            color: #e74c3c;
                            font-size: 12px;
                            font-weight: bold;
                            padding: 2px 8px;
                            background-color: #fadbd8;
                            border: 1px solid #e74c3c;
                            border-radius: 3px;
                        }
                    """)
        except Exception as e:
            self.ip_label.setText("IP地址: 获取失败")
            self.ip_label.setStyleSheet("""
                QLabel {
                    color: #e74c3c;
                    font-size: 12px;
                    font-weight: bold;
                    padding: 2px 8px;
                    background-color: #fadbd8;
                    border: 1px solid #e74c3c;
                    border-radius: 3px;
                }
            """)

    def get_current_ip(self) -> Optional[str]:
        """获取当前IP地址（返回第一个IP或所有IP的字符串）"""
        if self.current_ips:
            return self.current_ips[0]  # 返回第一个IP用于兼容性
        return None

    def get_all_current_ips(self) -> List[str]:
        """获取所有当前IP地址"""
        return self.current_ips

class StatusIndicator(QWidget):
    """状态指示器组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.current_status = "未知"
        
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 状态指示灯
        self.status_light = QLabel("●")
        self.status_light.setFixedSize(20, 20)
        self.status_light.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_light.setStyleSheet("color: gray; font-size: 16px;")
        
        # 状态文本
        self.status_text = QLabel("服务端状态: 未知")
        self.status_text.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        
        layout.addWidget(self.status_light)
        layout.addWidget(self.status_text)
        layout.addStretch()
        
    def update_status(self, status: str):
        """更新状态显示"""
        self.current_status = status
        self.status_text.setText(f"服务端状态: {status}")
        
        # 根据状态设置指示灯颜色
        color_map = {
            "运行中": "green",
            "初始化中": "orange", 
            "监控中": "blue",
            "错误": "red",
            "停止中": "orange",
            "监控已停止": "gray",
            "监控错误": "red",
            "未知": "gray"
        }
        
        color = color_map.get(status, "gray")
        self.status_light.setStyleSheet(f"color: {color}; font-size: 16px;")

class ServerLogViewer(QWidget):
    """服务端日志查看器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_entries = []
        self.filtered_entries = []
        self.current_filter = "全部"
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = self.create_toolbar()
        layout.addLayout(toolbar)
        
        # 日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(QFont("Consolas", 9))
        self.log_display.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)
        
        # 设置样式
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #3c3c3c;
            }
        """)
        
        layout.addWidget(self.log_display)
        
        # 设置日志级别颜色
        self.level_colors = {
            'DEBUG': QColor(150, 150, 150),    # 灰色
            'INFO': QColor(255, 255, 255),     # 白色
            'WARNING': QColor(255, 165, 0),    # 橙色
            'ERROR': QColor(255, 100, 100),    # 红色
            'CRITICAL': QColor(255, 0, 0)      # 亮红色
        }
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QHBoxLayout()
        
        # 日志级别筛选
        toolbar.addWidget(QLabel("级别:"))
        self.level_filter = QComboBox()
        self.level_filter.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_filter.currentTextChanged.connect(self.filter_logs)
        toolbar.addWidget(self.level_filter)
        
        # 清空按钮
        self.clear_button = QPushButton("清空")
        self.clear_button.clicked.connect(self.clear_logs)
        toolbar.addWidget(self.clear_button)
        
        # 暂停/继续按钮
        self.pause_button = QPushButton("暂停")
        self.pause_button.setCheckable(True)
        self.pause_button.clicked.connect(self.toggle_pause)
        toolbar.addWidget(self.pause_button)
        
        # 自动滚动复选框
        self.auto_scroll = QPushButton("自动滚动")
        self.auto_scroll.setCheckable(True)
        self.auto_scroll.setChecked(True)
        toolbar.addWidget(self.auto_scroll)
        
        toolbar.addStretch()
        
        return toolbar
        
    def add_log_entry(self, log_entry):
        """添加日志条目"""
        if self.pause_button.isChecked():
            return
            
        self.log_entries.append(log_entry)
        
        # 限制日志条目数量
        if len(self.log_entries) > 1000:
            self.log_entries = self.log_entries[-1000:]
            
        # 如果当前过滤器匹配，显示日志
        if self._matches_filter(log_entry):
            self._append_log_to_display(log_entry)
            
    def _matches_filter(self, log_entry) -> bool:
        """检查日志条目是否匹配当前过滤器"""
        if self.current_filter == "全部":
            return True
        return log_entry.level == self.current_filter
        
    def _append_log_to_display(self, log_entry):
        """将日志条目添加到显示区域"""
        cursor = self.log_display.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        
        # 设置文本格式
        format = QTextCharFormat()
        format.setForeground(self.level_colors.get(log_entry.level, QColor(255, 255, 255)))
        
        # 格式化日志文本
        log_text = f"[{log_entry.timestamp}] [{log_entry.level}] {log_entry.logger_name}: {log_entry.message}\n"
        
        cursor.setCharFormat(format)
        cursor.insertText(log_text)
        
        # 自动滚动到底部
        if self.auto_scroll.isChecked():
            scrollbar = self.log_display.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
    def filter_logs(self, filter_text: str):
        """过滤日志显示"""
        self.current_filter = filter_text
        self.refresh_display()
        
    def refresh_display(self):
        """刷新显示"""
        self.log_display.clear()
        
        for entry in self.log_entries:
            if self._matches_filter(entry):
                self._append_log_to_display(entry)
                
    def clear_logs(self):
        """清空日志"""
        self.log_entries.clear()
        self.log_display.clear()
        
    def toggle_pause(self):
        """切换暂停/继续状态"""
        if self.pause_button.isChecked():
            self.pause_button.setText("继续")
        else:
            self.pause_button.setText("暂停")

class ServerStatusWidget(QWidget):
    """服务端状态主组件"""
    
    def __init__(self, parent=None, show_logs=True):
        super().__init__(parent)
        self.show_logs = show_logs
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 状态指示器
        self.status_indicator = StatusIndicator()
        layout.addWidget(self.status_indicator)

        # 添加IP地址显示组件
        self.ip_display = IPAddressDisplay()
        layout.addWidget(self.ip_display)

        if self.show_logs:
            # 分隔线
            line = QFrame()
            line.setFrameShape(QFrame.Shape.HLine)
            line.setFrameShadow(QFrame.Shadow.Sunken)
            layout.addWidget(line)

            # 日志查看器
            log_group = QGroupBox("服务端日志")
            log_layout = QVBoxLayout(log_group)

            self.log_viewer = ServerLogViewer()
            log_layout.addWidget(self.log_viewer)

            layout.addWidget(log_group)
        else:
            # 不显示日志时，log_viewer设为None
            self.log_viewer = None
        

        
    def update_status(self, status: str):
        """更新状态显示"""
        self.status_indicator.update_status(status)
        
    def add_log_entry(self, log_entry):
        """添加日志条目"""
        if self.log_viewer:
            self.log_viewer.add_log_entry(log_entry)

    def on_monitoring_error(self, error_message: str):
        """处理监控错误"""
        if self.log_viewer:
            self.log_viewer.add_log_entry(type('LogEntry', (), {
                'timestamp': create_log_entry_timestamp(),
                'level': 'ERROR',
                'logger_name': 'monitor',
                'message': f"监控错误: {error_message}"
            })())

    def get_current_ip(self) -> Optional[str]:
        """获取当前IP地址"""
        return self.ip_display.get_current_ip()

    def refresh_ip_display(self):
        """刷新IP地址显示"""
        self.ip_display.update_ip_address()
