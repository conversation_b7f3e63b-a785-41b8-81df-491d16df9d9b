#!/usr/bin/env python3
"""
考勤系统统一启动脚本
同时启动服务端和桌面程序，并管理它们的生命周期
"""
import sys
import os
import time
import signal
import socket
import subprocess
import threading
import argparse
from pathlib import Path
from typing import Optional, Dict, Any
import logging
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
def get_project_root():
    """获取项目根目录"""
    if getattr(sys, 'frozen', False):
        # 打包环境：使用可执行文件所在目录
        return Path(sys.executable).parent
    else:
        # 开发环境：使用脚本文件所在目录
        return Path(__file__).parent

project_root = get_project_root()
sys.path.insert(0, str(project_root))

class ProcessManager:
    """进程管理器"""

    def __init__(self):
        self.server_process: Optional[subprocess.Popen] = None
        self.desktop_process: Optional[subprocess.Popen] = None
        self.server_thread: Optional[threading.Thread] = None
        self.desktop_thread: Optional[threading.Thread] = None
        self.is_shutting_down = False
        self.is_frozen = getattr(sys, 'frozen', False)  # 检测是否在打包环境中
        self.logger = self._setup_logger()

        # 在打包环境中，我们需要导入相关模块
        if self.is_frozen:
            self._import_modules()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ProcessManager')
        logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        return logger

    def _import_modules(self):
        """在打包环境中导入必要的模块"""
        try:
            # 在打包环境中，模块已经被包含，不需要特殊导入
            # 这里主要是确保模块路径正确
            self.logger.info("打包环境模块导入检查完成")
        except Exception as e:
            self.logger.error(f"模块导入检查失败: {e}")
    
    def check_port_available(self, host: str, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result != 0  # 0表示连接成功，即端口被占用
        except Exception as e:
            self.logger.warning(f"检查端口 {host}:{port} 时发生错误: {e}")
            return False
    
    def wait_for_server_ready(self, host: str, port: int, timeout: int = 30) -> bool:
        """等待服务端启动完成"""
        self.logger.info(f"等待服务端启动 {host}:{port}...")

        # 如果host是0.0.0.0，检查localhost
        check_host = "localhost" if host == "0.0.0.0" else host

        start_time = time.time()
        while time.time() - start_time < timeout:
            if not self.check_port_available(check_host, port):
                self.logger.info("服务端已启动并监听端口")
                return True
            time.sleep(1)
        
        self.logger.error(f"服务端在 {timeout} 秒内未能启动")
        return False
    
    def start_server(self, host: str = "0.0.0.0", port: int = 8000, debug: bool = False) -> bool:
        """启动服务端进程"""
        try:
            # 检查端口是否被占用（如果host是0.0.0.0，检查localhost）
            check_host = "localhost" if host == "0.0.0.0" else host
            if not self.check_port_available(check_host, port):
                self.logger.error(f"端口 {check_host}:{port} 已被占用")
                return False

            if self.is_frozen:
                # 打包环境：使用线程方式启动服务端
                return self._start_server_in_thread(host, port, debug)
            else:
                # 开发环境：使用子进程方式启动服务端
                return self._start_server_subprocess(host, port, debug)

        except Exception as e:
            self.logger.error(f"启动服务端时发生错误: {e}")
            return False

    def _start_server_subprocess(self, host: str, port: int, debug: bool) -> bool:
        """在开发环境中使用子进程启动服务端"""
        # 构建启动命令
        cmd = [
            sys.executable, "main.py",
            "--host", host,
            "--port", str(port)
        ]

        if debug:
            cmd.append("--reload")

        self.logger.info(f"启动服务端子进程: {' '.join(cmd)}")

        # 启动服务端进程
        self.server_process = subprocess.Popen(
            cmd,
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        # 启动日志监控线程
        log_thread = threading.Thread(
            target=self._monitor_server_logs,
            daemon=True
        )
        log_thread.start()

        # 等待服务端启动完成
        if self.wait_for_server_ready(host, port):
            self.logger.info("服务端启动成功")
            return True
        else:
                self.logger.error("服务端启动失败")
                self.stop_server()
                return False

    def _start_server_in_thread(self, host: str, port: int, debug: bool) -> bool:
        """在打包环境中使用线程启动服务端"""
        self.logger.info(f"在打包环境中启动服务端线程: {host}:{port}")

        def server_runner():
            """服务端运行函数"""
            try:
                # 设置环境变量
                os.environ['HOST'] = host
                os.environ['PORT'] = str(port)
                if debug:
                    os.environ['DEBUG'] = 'true'

                # 导入并启动服务端
                import uvicorn
                from attendance_server.main import app

                # 配置uvicorn
                config = uvicorn.Config(
                    app=app,
                    host=host,
                    port=port,
                    log_level="info" if not debug else "debug",
                    access_log=True
                )

                server = uvicorn.Server(config)

                # 在新的事件循环中运行服务器
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                self.logger.info(f"服务端开始监听 {host}:{port}")
                loop.run_until_complete(server.serve())

            except Exception as e:
                self.logger.error(f"服务端线程运行错误: {e}")
                import traceback
                traceback.print_exc()

        # 启动服务端线程
        self.server_thread = threading.Thread(
            target=server_runner,
            daemon=True,
            name="ServerThread"
        )
        self.server_thread.start()

        # 等待服务端启动完成
        if self.wait_for_server_ready(host, port, timeout=60):
            self.logger.info("服务端线程启动成功")
            return True
        else:
            self.logger.error("服务端线程启动失败")
            return False
    
    def _monitor_server_logs(self):
        """监控服务端日志输出"""
        if not self.server_process:
            return
            
        try:
            for line in iter(self.server_process.stdout.readline, ''):
                if line.strip():
                    self.logger.info(f"[SERVER] {line.strip()}")
                if self.server_process.poll() is not None:
                    break
        except Exception as e:
            self.logger.error(f"监控服务端日志时发生错误: {e}")
    
    def start_desktop(self) -> bool:
        """启动桌面程序"""
        try:
            if self.is_frozen:
                # 打包环境：使用线程方式启动桌面程序
                return self._start_desktop_in_thread()
            else:
                # 开发环境：使用子进程方式启动桌面程序
                return self._start_desktop_subprocess()

        except Exception as e:
            self.logger.error(f"启动桌面程序时发生错误: {e}")
            return False

    def _start_desktop_subprocess(self) -> bool:
        """在开发环境中使用子进程启动桌面程序"""
        cmd = [sys.executable, "main.py", "--desktop"]

        self.logger.info(f"启动桌面程序子进程: {' '.join(cmd)}")

        # 启动桌面程序
        self.desktop_process = subprocess.Popen(
            cmd,
            cwd=project_root
        )

        self.logger.info("桌面程序启动成功")
        return True

    def _start_desktop_in_thread(self) -> bool:
        """在打包环境中使用线程启动桌面程序"""
        self.logger.info("在打包环境中启动桌面程序线程")

        def desktop_runner():
            """桌面程序运行函数"""
            try:
                # 导入并启动桌面程序
                from desktop.main_window import MainWindow
                from PyQt6.QtWidgets import QApplication
                import sys

                # 创建QApplication实例
                app = QApplication(sys.argv)

                # 创建主窗口
                window = MainWindow()
                window.show()

                self.logger.info("桌面程序界面已显示")

                # 运行事件循环
                app.exec()

                self.logger.info("桌面程序已退出")

            except Exception as e:
                self.logger.error(f"桌面程序线程运行错误: {e}")
                import traceback
                traceback.print_exc()

        # 启动桌面程序线程
        self.desktop_thread = threading.Thread(
            target=desktop_runner,
            daemon=False,  # 不设为daemon，确保主程序等待桌面程序
            name="DesktopThread"
        )
        self.desktop_thread.start()

        self.logger.info("桌面程序线程启动成功")
        return True
    
    def stop_server(self):
        """停止服务端进程或线程"""
        if self.is_frozen and self.server_thread:
            # 打包环境：停止服务端线程
            try:
                self.logger.info("正在停止服务端线程...")
                # 在打包环境中，服务端线程是daemon线程，会自动停止
                # 这里主要是记录日志
                if self.server_thread.is_alive():
                    self.logger.info("服务端线程将随主程序退出")
                self.server_thread = None
                self.logger.info("服务端线程已标记停止")
            except Exception as e:
                self.logger.error(f"停止服务端线程时发生错误: {e}")

        elif self.server_process:
            # 开发环境：停止服务端进程
            try:
                self.logger.info("正在停止服务端进程...")
                self.server_process.terminate()

                # 等待进程结束
                try:
                    self.server_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    self.logger.warning("服务端未在10秒内停止，强制终止")
                    self.server_process.kill()
                    self.server_process.wait()

                self.logger.info("服务端进程已停止")
                self.server_process = None

            except Exception as e:
                self.logger.error(f"停止服务端进程时发生错误: {e}")
    
    def stop_desktop(self):
        """停止桌面程序进程或线程"""
        if self.is_frozen and self.desktop_thread:
            # 打包环境：等待桌面程序线程结束
            try:
                self.logger.info("正在等待桌面程序线程结束...")
                # 桌面程序线程不是daemon，会在用户关闭窗口时自然结束
                if self.desktop_thread.is_alive():
                    self.desktop_thread.join(timeout=5)
                self.desktop_thread = None
                self.logger.info("桌面程序线程已结束")
            except Exception as e:
                self.logger.error(f"等待桌面程序线程结束时发生错误: {e}")

        elif self.desktop_process:
            # 开发环境：停止桌面程序进程
            try:
                self.logger.info("正在停止桌面程序进程...")
                self.desktop_process.terminate()

                # 等待进程结束
                try:
                    self.desktop_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.logger.warning("桌面程序未在5秒内停止，强制终止")
                    self.desktop_process.kill()
                    self.desktop_process.wait()

                self.logger.info("桌面程序进程已停止")
                self.desktop_process = None

            except Exception as e:
                self.logger.error(f"停止桌面程序进程时发生错误: {e}")

    def wait_for_desktop_exit(self):
        """等待桌面程序退出"""
        if self.is_frozen and self.desktop_thread:
            # 打包环境：等待桌面程序线程
            try:
                self.desktop_thread.join()
                self.logger.info("桌面程序线程已退出")
            except Exception as e:
                self.logger.error(f"等待桌面程序线程退出时发生错误: {e}")
        elif self.desktop_process:
            # 开发环境：等待桌面程序进程
            try:
                self.desktop_process.wait()
                self.logger.info("桌面程序进程已退出")
            except Exception as e:
                self.logger.error(f"等待桌面程序退出时发生错误: {e}")
    
    def shutdown(self):
        """关闭所有进程"""
        if self.is_shutting_down:
            return
            
        self.is_shutting_down = True
        self.logger.info("开始关闭所有进程...")
        
        # 先停止桌面程序
        self.stop_desktop()
        
        # 再停止服务端
        self.stop_server()
        
        self.logger.info("所有进程已关闭")

def check_dependencies() -> bool:
    """检查依赖环境"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            return False
        
        # 检查必要的模块
        required_modules = ['fastapi', 'uvicorn', 'PyQt6', 'sqlalchemy']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            print(f"错误: 缺少必要的模块: {', '.join(missing_modules)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        return True
        
    except Exception as e:
        print(f"检查依赖时发生错误: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="考勤系统统一启动器")
    parser.add_argument("--host", default="0.0.0.0", help="服务端监听地址")
    parser.add_argument("--port", type=int, default=8000, help="服务端监听端口")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--server-only", action="store_true", help="仅启动服务端")
    parser.add_argument("--desktop-only", action="store_true", help="仅启动桌面程序")
    
    args = parser.parse_args()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建进程管理器
    manager = ProcessManager()
    
    # 设置信号处理器
    def signal_handler(signum, frame):
        print("\n收到中断信号，正在关闭...")
        manager.shutdown()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        success = True
        
        # 根据参数启动相应的程序
        if args.desktop_only:
            success = manager.start_desktop()
        elif args.server_only:
            success = manager.start_server(args.host, args.port, args.debug)
        else:
            # 默认：同时启动服务端和桌面程序
            success = manager.start_server(args.host, args.port, args.debug)
            if success:
                time.sleep(2)  # 等待服务端完全启动
                success = manager.start_desktop()
        
        if not success:
            print("启动失败")
            manager.shutdown()
            sys.exit(1)
        
        # 等待桌面程序退出（如果启动了桌面程序）
        if not args.server_only:
            manager.wait_for_desktop_exit()
        else:
            # 如果只启动服务端，则等待中断信号
            while True:
                time.sleep(1)
                if manager.server_process and manager.server_process.poll() is not None:
                    break
        
    except KeyboardInterrupt:
        print("\n用户中断，正在关闭...")
    except Exception as e:
        print(f"运行时发生错误: {e}")
    finally:
        manager.shutdown()

if __name__ == "__main__":
    main()
