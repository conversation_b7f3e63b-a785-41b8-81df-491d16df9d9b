import sys
import asyncio
import pandas as pd
from datetime import datetime, timezone, timedelta
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
    QWidget, QPushButton, QTableWidget, QTableWidgetItem,
    QLabel, QTextEdit, QSplitter, QGroupBox, QHeaderView,
    QFileDialog, QMessageBox, QTabWidget, QLineEdit, QDateEdit, QTimeEdit, QDialog
)
from PyQt6.QtCore import QTimer, QThread, pyqtSignal, QLocale, QTranslator, Qt, QThreadPool
from PyQt6.QtGui import QFont
from .ui.attendance_table import AttendanceTableWidget
from .ui.log_viewer import LogViewerWidget
from .ui.export_dialog import ExportDialog
from .ui.makeup_dialog import MakeupDialog
from .ui.server_status import ServerStatusWidget, ServerLogViewer
from .controllers.sync_controller import SyncController
from .utils.log_monitor import ServerStatusMonitor
from .utils.temp_monitor import SyncB<PERSON>onController
from .utils.udp_broadcast import UDPBroadcastService
from .utils.time_utils import (
    timestamp_to_beijing_string,
    get_current_date_string,
    create_log_entry_timestamp,
    parse_timestamp_for_export
)


class AsyncInitWorker(QThread):
    """异步初始化工作线程"""

    # 定义信号 - 用于线程安全的通信
    backup_recovery_completed = pyqtSignal()
    start_monitoring_requested = pyqtSignal()
    start_temp_monitoring_requested = pyqtSignal()
    start_udp_service_requested = pyqtSignal()
    initialization_completed = pyqtSignal()
    error_occurred = pyqtSignal(str)
    log_message = pyqtSignal(str, str)  # (level, message)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window

    def run(self):
        """在后台线程中执行耗时的初始化操作（仅非GUI操作）"""
        try:
            self.log_message.emit("信息", "开始异步初始化...")

            # 1. 执行temp目录备份和数据恢复（这个操作主要是文件操作，相对安全）
            self.perform_backup_and_recovery()
            self.backup_recovery_completed.emit()

            # 2-4. 发送信号请求主线程执行GUI相关操作
            self.start_monitoring_requested.emit()
            self.start_temp_monitoring_requested.emit()
            self.start_udp_service_requested.emit()

            # 所有初始化完成
            self.initialization_completed.emit()

        except Exception as e:
            self.error_occurred.emit(f"异步初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def perform_backup_and_recovery(self):
        """执行文件备份和恢复操作（非GUI操作）"""
        try:
            # 这里只执行文件操作，不涉及Qt对象
            from config.path_manager import get_temp_dir, get_temp_bak_dir, list_temp_files
            import shutil

            temp_dir = get_temp_dir()
            temp_bak_dir = get_temp_bak_dir()

            # 检查temp目录是否存在且包含文件
            if not temp_dir.exists():
                self.log_message.emit("提示", "temp目录不存在，跳过备份操作")
                return

            # 获取temp目录下的所有文件
            all_files = list_temp_files()
            if not all_files:
                self.log_message.emit("提示", "temp目录为空，跳过备份操作")
                return

            self.log_message.emit("信息", f"发现temp目录包含 {len(all_files)} 个文件，开始备份...")

            # 确保temp_bak目录存在
            temp_bak_dir.mkdir(parents=True, exist_ok=True)

            # 移动文件到temp_bak目录
            moved_count = 0
            for file_path in all_files:
                try:
                    dest_path = temp_bak_dir / file_path.name

                    # 如果目标文件已存在，先删除（实现覆盖）
                    if dest_path.exists():
                        dest_path.unlink()
                        self.log_message.emit("信息", f"覆盖已存在的文件: {file_path.name}")

                    # 移动文件
                    shutil.move(str(file_path), str(dest_path))
                    moved_count += 1
                    self.log_message.emit("信息", f"已移动文件: {file_path.name}")

                except Exception as e:
                    self.log_message.emit("错误", f"移动文件 {file_path.name} 失败: {str(e)}")
                    continue

            if moved_count > 0:
                self.log_message.emit("信息", f"成功移动 {moved_count} 个文件到temp_bak目录")

        except Exception as e:
            self.log_message.emit("错误", f"备份操作失败: {str(e)}")
            raise e
import threading
from pathlib import Path

# 语言配置相关函数
def get_font_family():
    """获取字体族"""
    try:
        from config.language_config import get_language_config
        config = get_language_config()
        return config.get_font_family()
    except ImportError:
        return "Microsoft YaHei"

def get_font_size():
    """获取字体大小"""
    try:
        from config.language_config import get_language_config
        config = get_language_config()
        return config.get_font_size()
    except ImportError:
        return 9

def setup_system_locale():
    """设置系统区域设置"""
    try:
        from config.language_config import setup_system_locale as _setup_system_locale
        _setup_system_locale()
    except ImportError:
        pass

def setup_environment_variables():
    """设置环境变量"""
    try:
        from config.language_config import setup_environment_variables as _setup_env
        _setup_env()
    except ImportError:
        pass

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("考勤系统管理端")
        self.setGeometry(100, 100, 1400, 900)

        # 初始化控制器
        self.sync_controller = SyncController()

        # 初始化服务端状态监控器
        self.server_monitor = ServerStatusMonitor()

        # 初始化同步状态控制
        self.sync_in_progress = False  # 同步进行中标志
        self.sync_lock = False  # 同步锁定标志

        # 初始化同步按钮控制器
        try:
            from config.path_manager import get_temp_dir
            temp_dir = get_temp_dir()
            self.sync_controller_enhanced = SyncButtonController(str(temp_dir))
            # 启动监控
            self.sync_controller_enhanced.start_monitoring()
        except Exception as e:
            print(f"初始化同步按钮控制器失败: {e}")
            self.sync_controller_enhanced = None

        # 初始化UI（快速显示界面）
        self.init_ui()
        self.setup_connections()

        # 启动定时器
        self.start_timers()

        # 显示启动状态
        self.show_startup_status()

        # 启动异步初始化（在后台执行耗时操作）
        self.start_async_initialization()

    def show_startup_status(self):
        """显示启动状态信息"""
        self.log_viewer.add_log("信息", "考勤系统管理端启动中...")
        self.log_viewer.add_log("信息", "界面已就绪，正在后台初始化服务...")

    def update_service_status(self, status_message: str):
        """更新服务状态显示"""
        try:
            # 在日志中显示状态
            self.log_viewer.add_log("信息", status_message)

            # 如果有状态栏，也在状态栏显示
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage(status_message, 5000)  # 显示5秒

        except Exception as e:
            print(f"更新服务状态失败: {e}")

    def _reset_sync_state(self):
        """重置同步状态"""
        try:
            self.sync_in_progress = False
            self.sync_lock = False
            self.sync_button.setEnabled(True)
            self.sync_button.setText("同步")
            self.log_viewer.add_log("信息", "同步状态已重置")
        except Exception as e:
            print(f"重置同步状态失败: {e}")

    def start_async_initialization(self):
        """启动异步初始化"""
        try:
            # 创建异步初始化工作线程
            self.async_init_worker = AsyncInitWorker(self)

            # 连接信号
            self.async_init_worker.backup_recovery_completed.connect(
                self.on_backup_recovery_completed
            )
            self.async_init_worker.start_monitoring_requested.connect(
                self.on_start_monitoring_requested
            )
            self.async_init_worker.start_temp_monitoring_requested.connect(
                self.on_start_temp_monitoring_requested
            )
            self.async_init_worker.start_udp_service_requested.connect(
                self.on_start_udp_service_requested
            )
            self.async_init_worker.initialization_completed.connect(
                self.on_async_initialization_completed
            )
            self.async_init_worker.error_occurred.connect(
                lambda error: self.log_viewer.add_log("错误", f"初始化错误: {error}")
            )
            self.async_init_worker.log_message.connect(
                lambda level, message: self.log_viewer.add_log(level, message)
            )

            # 启动异步初始化
            self.async_init_worker.start()

        except Exception as e:
            self.log_viewer.add_log("错误", f"启动异步初始化失败: {str(e)}")

    def on_backup_recovery_completed(self):
        """备份恢复完成的回调"""
        try:
            from config.path_manager import list_temp_bak_files

            # 检查数据源
            all_db_files = list_temp_bak_files("*.db")
            self.log_viewer.add_log("信息", f"初始化数据加载：发现 {len(all_db_files)} 个.db文件")

            # 在主线程中刷新数据显示
            self.attendance_table.load_temp_bak_records()

            # 统计加载结果
            total_records = self.attendance_table.rowCount()
            if total_records > 0:
                self.log_viewer.add_log("信息", f"✓ 数据备份和恢复已完成，加载了 {total_records} 条考勤记录")
            else:
                self.log_viewer.add_log("信息", "✓ 数据备份和恢复已完成，未找到考勤记录")

        except Exception as e:
            self.log_viewer.add_log("错误", f"刷新数据显示失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_start_monitoring_requested(self):
        """启动服务端监控的回调（在主线程中执行）"""
        try:
            self.auto_start_monitoring()
            self.log_viewer.add_log("信息", "✓ 服务端监控已启动")
        except Exception as e:
            self.log_viewer.add_log("错误", f"启动服务端监控失败: {str(e)}")

    def on_start_temp_monitoring_requested(self):
        """启动temp目录监控的回调（在主线程中执行）"""
        try:
            self.sync_controller_enhanced.start_monitoring()
            self.log_viewer.add_log("信息", "✓ temp目录监控已启动")
        except Exception as e:
            self.log_viewer.add_log("错误", f"启动temp目录监控失败: {str(e)}")

    def on_start_udp_service_requested(self):
        """启动UDP广播服务的回调（在主线程中执行）"""
        try:
            self.start_udp_broadcast_service()
            self.log_viewer.add_log("信息", "✓ UDP广播服务已启动")
        except Exception as e:
            self.log_viewer.add_log("错误", f"启动UDP广播服务失败: {str(e)}")

    def on_async_initialization_completed(self):
        """异步初始化完成的回调"""
        self.log_viewer.add_log("信息", "🎉 考勤系统管理端初始化完成，所有服务已就绪！")

    def execute_backup_and_display_flow(self):
        """执行完整的备份和显示流程（主线程版本）"""
        try:
            self.log_viewer.add_log("信息", "开始执行temp目录备份和数据显示流程...")

            # 1. 检查并备份temp目录
            backup_success = self.backup_temp_directory_if_needed()

            # 2. 无论备份是否成功，都尝试从temp_bak恢复和显示数据
            self.recover_and_display_data()

            if backup_success:
                self.log_viewer.add_log("信息", "备份和数据显示流程完成")
            else:
                self.log_viewer.add_log("信息", "数据显示流程完成（无需备份）")

        except Exception as e:
            self.log_viewer.add_log("错误", f"备份和显示流程失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def backup_temp_directory_if_needed(self):
        """检查temp目录并在需要时执行备份"""
        try:
            from config.path_manager import get_temp_dir, list_temp_files
            temp_dir = get_temp_dir()

            # 检查temp目录是否存在且包含文件
            if not temp_dir.exists():
                self.log_viewer.add_log("提示", "temp目录不存在，跳过备份操作")
                return False

            # 获取temp目录下的所有文件
            all_files = list_temp_files()
            if not all_files:
                self.log_viewer.add_log("提示", "temp目录为空，跳过备份操作")
                return False

            self.log_viewer.add_log("信息", f"发现temp目录包含 {len(all_files)} 个文件，开始备份...")

            # 执行备份操作
            backup_success = self.backup_temp_directory()

            if backup_success:
                self.log_viewer.add_log("信息", "temp目录备份完成")
                return True
            else:
                self.log_viewer.add_log("错误", "temp目录备份失败")
                return False

        except Exception as e:
            self.log_viewer.add_log("错误", f"检查和备份temp目录失败: {str(e)}")
            return False

    def backup_temp_directory(self):
        """备份temp目录到temp_bak目录"""
        import shutil

        try:
            from config.path_manager import get_temp_dir, get_temp_bak_dir, list_temp_files, list_temp_bak_files
            temp_dir = get_temp_dir()
            temp_bak_dir = get_temp_bak_dir()

            # 检查temp目录是否存在且包含文件
            if not temp_dir.exists():
                self.log_viewer.add_log("提示", "temp目录不存在，跳过备份操作")
                return True

            # 获取temp目录下的所有文件
            all_files = list_temp_files()
            if not all_files:
                self.log_viewer.add_log("提示", "temp目录为空，跳过备份操作")
                return True

            # 确保temp_bak目录存在
            temp_bak_dir.mkdir(parents=True, exist_ok=True)

            # 移动文件到temp_bak目录
            moved_count = 0
            for file_path in all_files:
                try:
                    dest_path = temp_bak_dir / file_path.name

                    # 如果目标文件已存在，先删除（实现覆盖）
                    if dest_path.exists():
                        dest_path.unlink()
                        self.log_viewer.add_log("信息", f"覆盖已存在的文件: {file_path.name}")

                    # 移动文件
                    shutil.move(str(file_path), str(dest_path))
                    moved_count += 1
                    self.log_viewer.add_log("信息", f"已移动文件: {file_path.name}")

                except Exception as e:
                    self.log_viewer.add_log("错误", f"移动文件 {file_path.name} 失败: {str(e)}")
                    continue

            self.log_viewer.add_log("信息", f"成功移动 {moved_count} 个文件到temp_bak目录")
            return moved_count > 0

        except Exception as e:
            self.log_viewer.add_log("错误", f"备份temp目录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def recover_and_display_data(self):
        """从temp_bak目录恢复数据并显示在表格中"""
        try:
            from config.path_manager import get_temp_bak_dir, list_temp_bak_files
            temp_bak_dir = get_temp_bak_dir()

            # 检查temp_bak目录是否存在
            if not temp_bak_dir.exists():
                self.log_viewer.add_log("提示", "temp_bak目录不存在，无法恢复数据")
                return

            # 获取temp_bak目录下的所有文件
            all_files = list_temp_bak_files()
            if not all_files:
                self.log_viewer.add_log("提示", "temp_bak目录为空，无数据可恢复")
                return

            self.log_viewer.add_log("信息", f"开始从temp_bak目录恢复数据，共 {len(all_files)} 个文件")

            # 解析并显示数据
            self.parse_and_display_data(all_files)

        except Exception as e:
            self.log_viewer.add_log("错误", f"恢复和显示数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_search_clicked(self):
        """搜索按钮点击事件"""
        try:
            search_text = self.search_input.text().strip()
            if not search_text:
                self.log_viewer.add_log("提示", "请输入搜索关键词")
                return

            self.log_viewer.add_log("信息", f"开始搜索姓名包含 '{search_text}' 的记录...")

            # 确保数据是最新的，然后执行搜索
            self.search_records_with_refresh(search_text)

        except Exception as e:
            self.log_viewer.add_log("错误", f"搜索操作失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def search_records_with_refresh(self, search_text):
        """搜索记录（直接从数据库文件搜索，不需要预加载）"""
        try:
            # 新的搜索逻辑直接从数据库文件搜索，不需要预加载表格数据
            self.search_records(search_text)

        except Exception as e:
            self.log_viewer.add_log("错误", f"搜索记录失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_clear_search_clicked(self):
        """清空搜索按钮点击事件"""
        try:
            self.search_input.clear()
            self.log_viewer.add_log("信息", "清空搜索条件，重新加载全部记录...")

            # 重新加载所有记录（不再是显示隐藏的行，而是重新从数据库加载）
            self.refresh_attendance_data()

            # 禁用补卡按钮
            self.makeup_button.setEnabled(False)

            self.log_viewer.add_log("信息", "已显示全部记录")

        except Exception as e:
            self.log_viewer.add_log("错误", f"清空搜索失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def search_records(self, search_text):
        """直接从数据库文件中搜索记录"""
        try:
            from config.path_manager import get_temp_bak_dir, list_temp_bak_files
            from desktop.utils.time_utils import timestamp_to_beijing_string
            import sqlite3
            from PyQt6.QtWidgets import QTableWidgetItem

            self.log_viewer.add_log("信息", f"开始从数据库文件中搜索 '{search_text}'...")

            # 获取所有数据库文件
            all_db_files = list_temp_bak_files("*.db")
            self.log_viewer.add_log("信息", f"搜索范围：{len(all_db_files)} 个数据库文件")

            if not all_db_files:
                self.log_viewer.add_log("提示", "没有找到数据库文件")
                self.attendance_table.setRowCount(0)
                self.makeup_button.setEnabled(False)
                return

            # 收集所有匹配的记录
            all_matched_records = []
            processed_files = 0
            total_matched = 0

            for db_file in all_db_files:
                try:
                    conn = sqlite3.connect(str(db_file))
                    cursor = conn.cursor()

                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='KAO_QIN__RECORD'")
                    if not cursor.fetchone():
                        conn.close()
                        continue

                    # 执行搜索查询
                    cursor.execute("""
                        SELECT _id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM
                        FROM KAO_QIN__RECORD
                        WHERE SFZ_NAME LIKE ?
                        ORDER BY ENTER_TIME DESC
                    """, (f'%{search_text}%',))

                    records = cursor.fetchall()
                    file_matched_count = len(records)

                    if file_matched_count > 0:
                        all_matched_records.extend(records)
                        total_matched += file_matched_count
                        self.log_viewer.add_log("信息", f"文件 {db_file.name}: 找到 {file_matched_count} 条匹配记录")

                    processed_files += 1
                    conn.close()

                except Exception as e:
                    self.log_viewer.add_log("错误", f"搜索文件 {db_file.name} 失败: {str(e)}")
                    continue

            # 处理搜索结果
            if all_matched_records:
                # 按时间倒序排序
                all_matched_records.sort(key=lambda x: x[4] if x[4] else 0, reverse=True)

                # 在表格中显示搜索结果
                self.display_search_results(all_matched_records, search_text)

                self.log_viewer.add_log("信息", f"搜索完成：从 {processed_files} 个文件中找到 {total_matched} 条匹配记录")

            else:
                self.log_viewer.add_log("提示", f"在所有数据库文件中未找到姓名包含 '{search_text}' 的记录")
                self.attendance_table.setRowCount(0)
                self.makeup_button.setEnabled(False)

        except Exception as e:
            self.log_viewer.add_log("错误", f"搜索记录失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def display_search_results(self, matched_records, search_text):
        """在表格中显示搜索结果"""
        try:
            from desktop.utils.time_utils import timestamp_to_beijing_string
            from PyQt6.QtWidgets import QTableWidgetItem

            # 设置表格行数
            self.attendance_table.setRowCount(len(matched_records))

            # 填充搜索结果到表格
            for row, record in enumerate(matched_records):
                # record格式: (_id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM)
                # 第1列：姓名（record[1]）
                self.attendance_table.setItem(row, 0, QTableWidgetItem(str(record[1] or "")))
                # 第2列：人员编号（record[2] - 身份证号）
                self.attendance_table.setItem(row, 1, QTableWidgetItem(str(record[2] or "")))
                # 第3列：组别（record[3]）
                self.attendance_table.setItem(row, 2, QTableWidgetItem(str(record[3] or "")))
                # 第4列：打卡时间（record[4]）- 使用北京时间
                timestamp = record[4]
                time_str = timestamp_to_beijing_string(timestamp) if timestamp else ""
                self.attendance_table.setItem(row, 3, QTableWidgetItem(time_str))

            # 确保所有行都可见（清除之前的隐藏状态）
            for row in range(self.attendance_table.rowCount()):
                self.attendance_table.setRowHidden(row, False)

            # 清除当前选择
            self.attendance_table.clearSelection()

            # 禁用补卡按钮，直到用户选择记录
            self.makeup_button.setEnabled(False)

            self.log_viewer.add_log("信息", f"搜索结果已显示：{len(matched_records)} 条记录")

        except Exception as e:
            self.log_viewer.add_log("错误", f"显示搜索结果失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_table_selection_changed(self):
        """表格选择变化事件"""
        try:
            selected_items = self.attendance_table.selectedItems()

            # 检查是否有选中的行且当前是搜索状态
            if selected_items and self.search_input.text().strip():
                # 启用补卡按钮
                self.makeup_button.setEnabled(True)
                self.log_viewer.add_log("信息", "已选中记录，补卡功能已启用")
            else:
                # 禁用补卡按钮
                self.makeup_button.setEnabled(False)

        except Exception as e:
            self.log_viewer.add_log("错误", f"处理表格选择变化失败: {str(e)}")

    def on_makeup_clicked(self):
        """补卡按钮点击事件"""
        try:
            # 检查是否有选中的记录
            selected_items = self.attendance_table.selectedItems()
            if not selected_items:
                self.log_viewer.add_log("提示", "请先选中一条考勤记录")
                QMessageBox.warning(self, "补卡失败", "请先选中一条考勤记录")
                return

            # 获取选中行的数据
            selected_row = self.attendance_table.currentRow()
            if selected_row < 0:
                self.log_viewer.add_log("提示", "请先选中一条考勤记录")
                QMessageBox.warning(self, "补卡失败", "请先选中一条考勤记录")
                return

            # 提取选中记录的信息
            name_item = self.attendance_table.item(selected_row, 0)
            employee_id_item = self.attendance_table.item(selected_row, 1)
            group_item = self.attendance_table.item(selected_row, 2)

            selected_record_data = {
                'name': name_item.text() if name_item else '',
                'employee_id': employee_id_item.text() if employee_id_item else '',
                'group': group_item.text() if group_item else ''
            }

            self.log_viewer.add_log("信息", f"开始为 {selected_record_data['name']} 进行补卡...")

            # 显示补卡对话框
            dialog = MakeupDialog(selected_record_data, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 获取补卡数据
                makeup_data = dialog.get_makeup_data()

                # 执行补卡操作
                self.perform_makeup(makeup_data)
            else:
                self.log_viewer.add_log("提示", "补卡操作已取消")

        except Exception as e:
            self.log_viewer.add_log("错误", f"补卡操作失败: {str(e)}")
            QMessageBox.critical(self, "补卡失败", f"补卡操作失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def perform_makeup(self, makeup_data):
        """执行补卡操作"""
        import sqlite3
        from datetime import datetime
        import time

        try:
            # 获取temp_bak目录路径
            from config.path_manager import get_temp_bak_dir
            temp_bak_dir = get_temp_bak_dir()

            if not temp_bak_dir.exists():
                self.log_viewer.add_log("错误", "temp_bak目录不存在")
                QMessageBox.critical(self, "补卡失败", "temp_bak目录不存在")
                return

            # 获取或创建补卡专用数据库文件
            makeup_db_file = temp_bak_dir / "补卡.db"
            self.log_viewer.add_log("信息", f"使用补卡专用数据库: {makeup_db_file.name}")

            # 确保补卡数据库存在并具有正确的表结构
            self.ensure_makeup_database(makeup_db_file)

            # 执行补卡插入操作
            self.insert_makeup_record(makeup_db_file, makeup_data)

        except Exception as e:
            self.log_viewer.add_log("错误", f"执行补卡操作失败: {str(e)}")
            QMessageBox.critical(self, "补卡失败", f"执行补卡操作失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def ensure_makeup_database(self, makeup_db_file):
        """确保补卡数据库存在并具有正确的表结构"""
        import sqlite3

        try:
            # 检查文件是否存在
            if not makeup_db_file.exists():
                self.log_viewer.add_log("信息", "补卡数据库不存在，正在创建...")

            # 连接数据库（如果不存在会自动创建）
            conn = sqlite3.connect(str(makeup_db_file))
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='KAO_QIN__RECORD'
            """)

            if not cursor.fetchone():
                # 创建表结构（与原数据库保持一致）
                cursor.execute("""
                    CREATE TABLE KAO_QIN__RECORD (
                        _id INTEGER PRIMARY KEY,
                        SFZ_NAME TEXT,
                        SFZ_ID TEXT,
                        SFZ_GROUP TEXT,
                        ENTER_TIME INTEGER,
                        MD5SUM TEXT
                    )
                """)

                self.log_viewer.add_log("信息", "已创建补卡数据库表结构")

            conn.commit()
            conn.close()

            self.log_viewer.add_log("信息", f"补卡数据库已就绪: {makeup_db_file.name}")

        except Exception as e:
            self.log_viewer.add_log("错误", f"创建补卡数据库失败: {str(e)}")
            raise e

    def insert_makeup_record(self, db_file, makeup_data):
        """向补卡数据库插入补卡记录"""
        import sqlite3
        from datetime import datetime
        import time
        import hashlib

        try:
            self.log_viewer.add_log("信息", f"正在向补卡数据库插入记录: {makeup_data['name']} - {makeup_data['datetime']}")

            # 连接补卡数据库
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()

            # 将补卡时间转换为时间戳（毫秒级，与系统其他部分保持一致）
            # 明确指定为北京时区，确保跨平台时区一致性
            from desktop.utils.time_utils import BEIJING_TZ
            makeup_datetime = datetime.strptime(makeup_data['datetime'], '%Y-%m-%d %H:%M:%S')
            makeup_datetime = makeup_datetime.replace(tzinfo=BEIJING_TZ)  # 明确指定为北京时区
            timestamp = int(makeup_datetime.timestamp() * 1000)  # 转换为毫秒时间戳

            # 生成MD5校验值
            md5_input = f"{makeup_data['name']}{makeup_data['employee_id']}{timestamp}"
            md5_hash = hashlib.md5(md5_input.encode()).hexdigest()

            # 获取下一个ID
            cursor.execute("SELECT MAX(_id) FROM KAO_QIN__RECORD")
            max_id_result = cursor.fetchone()
            max_id = max_id_result[0] if max_id_result[0] is not None else 0
            next_id = max_id + 1

            # 插入补卡记录
            cursor.execute("""
                INSERT INTO KAO_QIN__RECORD (_id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (next_id, makeup_data['name'], makeup_data['employee_id'],
                  makeup_data['group'], timestamp, md5_hash))

            conn.commit()
            conn.close()

            # 验证时间处理的正确性
            from desktop.utils.time_utils import timestamp_to_beijing_string
            stored_time_display = timestamp_to_beijing_string(timestamp)

            self.log_viewer.add_log("信息", f"补卡记录已成功插入到补卡.db文件中")
            self.log_viewer.add_log("信息", f"补卡成功：为 {makeup_data['name']} 添加了 {makeup_data['datetime']} 的考勤记录")
            self.log_viewer.add_log("信息", f"时间处理详情：")
            self.log_viewer.add_log("信息", f"  用户输入时间: {makeup_data['datetime']}")
            self.log_viewer.add_log("信息", f"  带时区datetime: {makeup_datetime}")
            self.log_viewer.add_log("信息", f"  生成时间戳: {timestamp} (毫秒级)")
            self.log_viewer.add_log("信息", f"  存储后显示: {stored_time_display}")
            self.log_viewer.add_log("信息", f"  时间一致性: {'✓' if stored_time_display == makeup_data['datetime'] else '✗'}")

            QMessageBox.information(self, "补卡成功",
                                  f"为 {makeup_data['name']} 补卡成功！\n时间：{makeup_data['datetime']}\n记录已保存到补卡.db文件中")

            # 刷新表格显示
            self.refresh_table_after_makeup()

        except Exception as e:
            self.log_viewer.add_log("错误", f"插入补卡记录失败: {str(e)}")
            QMessageBox.critical(self, "补卡失败", f"插入补卡记录失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def refresh_table_after_makeup(self):
        """补卡后刷新表格"""
        try:
            from config.path_manager import list_temp_bak_files

            # 检查数据源
            all_db_files = list_temp_bak_files("*.db")
            self.log_viewer.add_log("信息", f"补卡后刷新：基于 {len(all_db_files)} 个数据库文件重新加载数据")

            # 重新加载数据
            self.attendance_table.load_temp_bak_records()

            # 统计加载结果
            total_records = self.attendance_table.rowCount()
            self.log_viewer.add_log("信息", f"数据重新加载完成：共 {total_records} 条记录（无数量限制）")

            # 如果有搜索条件，重新执行完整搜索流程
            search_text = self.search_input.text().strip()
            if search_text:
                self.log_viewer.add_log("信息", f"重新执行搜索：'{search_text}'")
                # 使用完整的搜索流程，确保基于最新数据
                self.search_records_with_refresh(search_text)
            else:
                # 没有搜索条件，显示所有行
                for row in range(self.attendance_table.rowCount()):
                    self.attendance_table.setRowHidden(row, False)

            self.log_viewer.add_log("信息", "补卡后表格数据刷新完成")

        except Exception as e:
            self.log_viewer.add_log("错误", f"刷新表格失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def parse_and_display_data(self, file_list):
        """解析数据库文件并显示在表格中"""
        import sqlite3
        from datetime import datetime

        try:
            # 使用表格的专用方法加载temp_bak记录
            self.attendance_table.load_temp_bak_records()

            # 获取记录统计信息
            records_info = self.attendance_table.get_records_info()
            db_files = [f for f in file_list if f.name.lower().endswith('.db')]

            if records_info['total'] > 0:
                if records_info['is_limited']:
                    self.log_viewer.add_log("信息", f"数据恢复完成：共处理 {len(db_files)} 个数据库文件")
                    self.log_viewer.add_log("信息", f"共有 {records_info['total']} 条考勤记录，为了性能考虑，显示最新的 {records_info['displayed']} 条")
                    self.log_viewer.add_log("提示", f"如需查看更多记录，请使用搜索功能或导出功能")
                else:
                    self.log_viewer.add_log("信息", f"数据恢复完成：共处理 {len(db_files)} 个数据库文件，加载 {records_info['total']} 条考勤记录")
            else:
                self.log_viewer.add_log("提示", "未找到任何有效的考勤记录")

        except Exception as e:
            self.log_viewer.add_log("错误", f"解析和显示数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 垂直布局
        main_layout = QVBoxLayout(central_widget)

        # 顶部：服务状态区域（状态指示器和IP地址）
        top_panel = self.create_top_panel()

        # 中部：主要工作区域（考勤记录和操作控制）
        middle_panel = self.create_middle_panel()

        # 底部：统一日志显示区域
        bottom_panel = self.create_unified_log_panel()

        # 垂直分割器
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        main_splitter.addWidget(top_panel)
        main_splitter.addWidget(middle_panel)
        main_splitter.addWidget(bottom_panel)
        main_splitter.setSizes([120, 500, 280])  # 顶部120px，中部500px，底部280px

        main_layout.addWidget(main_splitter)
    
    def create_top_panel(self):
        """创建顶部面板 - 服务状态和IP地址显示"""
        top_widget = QWidget()
        layout = QHBoxLayout(top_widget)

        # 创建简化的服务状态组件（只包含状态指示器和IP地址）
        self.server_status_widget = ServerStatusWidget(show_logs=False)
        layout.addWidget(self.server_status_widget)

        return top_widget

    def create_middle_panel(self):
        """创建中部面板 - 主要工作区域"""
        middle_widget = QWidget()
        layout = QHBoxLayout(middle_widget)

        # 左侧：操作控制区域（简化版）
        left_panel = self.create_control_panel()

        # 右侧：考勤记录表格（移除系统日志）
        right_panel = self.create_attendance_panel()

        # 水平分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([300, 1100])  # 左侧300px，右侧1100px

        layout.addWidget(splitter)
        return middle_widget

    def create_control_panel(self):
        """创建操作控制面板"""
        control_widget = QWidget()
        layout = QVBoxLayout(control_widget)

        # 操作控制按钮组
        control_group = QGroupBox("操作控制")
        control_layout = QVBoxLayout(control_group)

        # 同步按钮
        self.sync_button = QPushButton("同步")
        self.sync_button.setFont(QFont(get_font_family(), 14))
        self.sync_button.setMinimumHeight(50)
        control_layout.addWidget(self.sync_button)

        # 导出按钮
        self.export_button = QPushButton("导出数据")
        self.export_button.setFont(QFont(get_font_family(), 14))
        self.export_button.setMinimumHeight(50)
        control_layout.addWidget(self.export_button)

        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setFont(QFont(get_font_family(), 14))
        self.refresh_button.setMinimumHeight(50)
        control_layout.addWidget(self.refresh_button)

        # 添加一些间距
        control_layout.addStretch()

        layout.addWidget(control_group)

        # 搜索功能组
        search_group = QGroupBox("搜索功能")
        search_layout = QVBoxLayout(search_group)

        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("请输入姓名进行搜索")
        self.search_input.setFont(QFont(get_font_family(), 12))
        self.search_input.setMinimumHeight(35)
        search_layout.addWidget(self.search_input)

        # 搜索按钮布局
        search_button_layout = QHBoxLayout()

        # 搜索按钮
        self.search_button = QPushButton("搜索")
        self.search_button.setFont(QFont(get_font_family(), 12))
        self.search_button.setMinimumHeight(35)
        search_button_layout.addWidget(self.search_button)

        # 清空搜索按钮
        self.clear_search_button = QPushButton("显示全部")
        self.clear_search_button.setFont(QFont(get_font_family(), 12))
        self.clear_search_button.setMinimumHeight(35)
        search_button_layout.addWidget(self.clear_search_button)

        search_layout.addLayout(search_button_layout)

        layout.addWidget(search_group)

        # 补卡功能组
        makeup_group = QGroupBox("补卡功能")
        makeup_layout = QVBoxLayout(makeup_group)

        # 补卡按钮
        self.makeup_button = QPushButton("补卡")
        self.makeup_button.setFont(QFont(get_font_family(), 14))
        self.makeup_button.setMinimumHeight(50)
        self.makeup_button.setEnabled(False)  # 默认禁用
        makeup_layout.addWidget(self.makeup_button)

        # 补卡说明标签
        makeup_info = QLabel("请先搜索并选中记录后进行补卡")
        makeup_info.setFont(QFont(get_font_family(), 10))
        makeup_info.setStyleSheet("color: gray;")
        makeup_layout.addWidget(makeup_info)

        layout.addWidget(makeup_group)

        layout.addStretch()  # 添加弹性空间

        return control_widget

    def create_attendance_panel(self):
        """创建考勤记录面板"""
        attendance_widget = QWidget()
        layout = QVBoxLayout(attendance_widget)

        # 考勤数据表格
        attendance_group = QGroupBox("考勤记录")
        attendance_layout = QVBoxLayout(attendance_group)

        self.attendance_table = AttendanceTableWidget()
        attendance_layout.addWidget(self.attendance_table)

        layout.addWidget(attendance_group)

        return attendance_widget

    def create_unified_log_panel(self):
        """创建统一的日志显示面板"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)

        # 使用标签页来区分不同类型的日志
        self.log_tabs = QTabWidget()

        # 服务端日志标签页
        server_log_widget = QWidget()
        server_log_layout = QVBoxLayout(server_log_widget)

        # 创建服务端日志查看器（从ServerStatusWidget中提取）
        self.server_log_viewer = ServerLogViewer()
        server_log_layout.addWidget(self.server_log_viewer)

        # 系统日志标签页
        system_log_widget = QWidget()
        system_log_layout = QVBoxLayout(system_log_widget)

        self.log_viewer = LogViewerWidget()
        system_log_layout.addWidget(self.log_viewer)

        # 添加标签页
        self.log_tabs.addTab(server_log_widget, "服务端日志")
        self.log_tabs.addTab(system_log_widget, "系统日志")

        layout.addWidget(self.log_tabs)

        return log_widget
    
    def setup_connections(self):
        """设置信号连接"""
        # 操作按钮连接
        self.sync_button.clicked.connect(self.on_sync_clicked)
        self.export_button.clicked.connect(self.on_export_clicked)
        self.refresh_button.clicked.connect(self.on_refresh_clicked)

        # 搜索功能连接
        self.search_button.clicked.connect(self.on_search_clicked)
        self.clear_search_button.clicked.connect(self.on_clear_search_clicked)
        self.search_input.returnPressed.connect(self.on_search_clicked)  # 回车键搜索

        # 补卡功能连接
        self.makeup_button.clicked.connect(self.on_makeup_clicked)

        # 表格选择变化连接
        self.attendance_table.itemSelectionChanged.connect(self.on_table_selection_changed)

        # 服务端监控器信号连接
        self.server_monitor.status_changed.connect(self.server_status_widget.update_status)
        self.server_monitor.log_received.connect(self.server_log_viewer.add_log_entry)
        self.server_monitor.error_occurred.connect(self.on_server_monitoring_error)

        # 同步按钮控制器信号连接
        if self.sync_controller_enhanced is not None:
            self.sync_controller_enhanced.button_state_changed.connect(self.update_sync_button_state)
            self.sync_controller_enhanced.sync_completed.connect(self.on_sync_completed)
    
    def start_timers(self):
        """启动定时器"""
        # 考勤数据更新定时器 - 修改：禁用定时刷新，只保留手动刷新
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.update_attendance_data)
        # 注释掉定时启动，避免偶发性崩溃
        # QTimer.singleShot(30000, lambda: self.data_timer.start(30000))  # 30秒后开始，每30秒更新一次

        # 添加日志说明
        self.log_viewer.add_log("信息", "定时刷新已禁用，请使用手动刷新按钮更新数据")

    def auto_start_monitoring(self):
        """自动启动服务端监控"""
        try:
            from config.path_manager import path_manager
            log_file_path = path_manager.get_server_log_file_path("server.log")

            # 启动监控
            self.start_server_monitoring(str(log_file_path))
            self.log_viewer.add_log("信息", "已自动启动服务端监控")

        except Exception as e:
            self.log_viewer.add_log("错误", f"自动启动服务端监控失败: {str(e)}")
    
    def on_sync_clicked(self):
        """同步按钮点击事件 - 增强版本，包含防重复机制"""
        try:
            # 防重复点击检查
            if self.sync_in_progress or self.sync_lock:
                self.log_viewer.add_log("警告", "同步操作正在进行中，请勿重复点击")
                return

            # 设置同步锁定
            self.sync_lock = True
            self.sync_in_progress = True

            # 立即禁用按钮，防止重复点击
            self.sync_button.setEnabled(False)
            self.sync_button.setText("准备同步...")

            self.log_viewer.add_log("信息", "开始同步操作...")

            # 1. 删除temp目录中的所有文件
            from config.path_manager import get_temp_dir, get_temp_cfg_dir
            temp_dir = get_temp_dir()

            # 确保temp目录存在
            if not temp_dir.exists():
                try:
                    temp_dir.mkdir(parents=True)
                    self.log_viewer.add_log("信息", f"已创建temp目录: {temp_dir}")
                except Exception as e:
                    self.log_viewer.add_log("错误", f"创建temp目录失败: {str(e)}")
                    return

            # 删除temp目录中的所有文件
            deleted_count = 0
            try:
                for file_path in temp_dir.iterdir():
                    if file_path.is_file():
                        file_path.unlink()
                        deleted_count += 1
                self.log_viewer.add_log("信息", f"已删除temp目录中的 {deleted_count} 个文件")
            except Exception as e:
                self.log_viewer.add_log("错误", f"删除temp目录文件失败: {str(e)}")
                self._reset_sync_state()
                return

            # 2. 将按钮文字更改为"同步中"
            self.sync_button.setText("同步中")
            self.log_viewer.add_log("信息", "同步按钮状态改为'同步中'")

            # 3. 启动temp目录监控
            if not hasattr(self, 'sync_controller_enhanced') or self.sync_controller_enhanced is None:
                # 如果控制器不存在，重新创建
                try:
                    from desktop.utils.temp_monitor import SyncButtonController
                    self.sync_controller_enhanced = SyncButtonController(str(temp_dir))
                    # 重新连接信号
                    self.sync_controller_enhanced.button_state_changed.connect(self.update_sync_button_state)
                    self.sync_controller_enhanced.sync_completed.connect(self.on_sync_completed)
                except Exception as e:
                    self.log_viewer.add_log("错误", f"创建同步控制器失败: {str(e)}")
                    self._reset_sync_state()
                    return

            # 开始监控temp目录
            try:
                if not self.sync_controller_enhanced.temp_monitor.isRunning():
                    self.sync_controller_enhanced.start_monitoring()
                self.log_viewer.add_log("信息", "已启动temp目录监控")
            except Exception as e:
                self.log_viewer.add_log("错误", f"启动temp目录监控失败: {str(e)}")
                self._reset_sync_state()
                return

            # 关键修复：设置同步状态
            try:
                if self.sync_controller_enhanced.start_sync():
                    self.log_viewer.add_log("信息", "已设置同步状态，开始监控文件变化")
                else:
                    self.log_viewer.add_log("错误", "设置同步状态失败")
                    self._reset_sync_state()
                    return
            except Exception as e:
                self.log_viewer.add_log("错误", f"设置同步状态失败: {str(e)}")
                self._reset_sync_state()
                return

            # 4. 创建update.txt文件触发设备上传
            temp_cfg_dir = get_temp_cfg_dir()
            update_file = temp_cfg_dir / "update.txt"
            update_file.touch()
            self.log_viewer.add_log("信息", "已创建update.txt文件，等待设备响应...")

        except Exception as e:
            self.log_viewer.add_log("错误", f"同步操作失败: {str(e)}")
            self._reset_sync_state()

    def on_refresh_clicked(self):
        """刷新按钮点击事件 - 增强异常处理"""
        try:
            # 检查UI组件是否可用
            if not hasattr(self, 'log_viewer') or not self.log_viewer:
                return

            self.log_viewer.add_log("信息", "开始执行刷新操作...")

            # 禁用刷新按钮，防止重复点击
            if hasattr(self, 'refresh_button') and self.refresh_button:
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("刷新中...")

            try:
                # 直接重新加载temp_bak目录中的所有.db文件数据
                self.refresh_attendance_data()
                self.log_viewer.add_log("信息", "刷新操作完成")
            finally:
                # 恢复刷新按钮状态
                if hasattr(self, 'refresh_button') and self.refresh_button:
                    self.refresh_button.setEnabled(True)
                    self.refresh_button.setText("刷新")

        except Exception as e:
            if hasattr(self, 'log_viewer') and self.log_viewer:
                self.log_viewer.add_log("错误", f"刷新操作失败: {str(e)}")
            else:
                print(f"刷新操作失败: {str(e)}")
            import traceback
            traceback.print_exc()

            # 确保按钮状态恢复
            if hasattr(self, 'refresh_button') and self.refresh_button:
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("刷新")

    def refresh_attendance_data(self):
        """刷新考勤数据 - 统一的数据加载逻辑"""
        try:
            from config.path_manager import get_temp_bak_dir, list_temp_bak_files

            # 检查temp_bak目录
            temp_bak_dir = get_temp_bak_dir()
            if not temp_bak_dir.exists():
                self.log_viewer.add_log("提示", "temp_bak目录不存在")
                self.attendance_table.setRowCount(0)
                return

            # 获取所有.db文件
            all_db_files = list_temp_bak_files("*.db")
            self.log_viewer.add_log("信息", f"发现 {len(all_db_files)} 个.db文件: {[f.name for f in all_db_files]}")

            if not all_db_files:
                self.log_viewer.add_log("提示", "temp_bak目录中没有找到.db文件")
                self.attendance_table.setRowCount(0)
                return

            # 使用表格的统一加载方法
            self.attendance_table.load_temp_bak_records()

            # 统计信息
            total_records = self.attendance_table.rowCount()
            if total_records > 0:
                self.log_viewer.add_log("信息", f"刷新完成：从 {len(all_db_files)} 个数据库文件加载了 {total_records} 条考勤记录")
            else:
                self.log_viewer.add_log("提示", "未找到任何有效的考勤记录")

        except Exception as e:
            self.log_viewer.add_log("错误", f"刷新考勤数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def process_temp_files(self):
        """处理temp目录中的文件并显示考勤记录"""
        import sqlite3
        from datetime import datetime

        try:
            # 获取temp目录路径
            from config.path_manager import get_temp_dir, list_temp_files
            temp_dir = get_temp_dir()
            if not temp_dir.exists():
                self.log_viewer.add_log("提示", "temp目录不存在")
                return

            # 获取temp目录下的所有文件
            all_files = list_temp_files()
            if not all_files:
                self.log_viewer.add_log("提示", "temp目录下没有找到任何文件")
                return

            # 清除现有的表格内容
            self.attendance_table.setRowCount(0)

            # 创建一个合并的记录列表
            all_records = []

            # 从每个文件中读取记录
            for file_path in all_files:
                file_name = file_path.name
                
                try:
                    # 尝试作为SQLite数据库打开文件
                    conn = sqlite3.connect(str(file_path))
                    cursor = conn.cursor()
                    
                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='KAO_QIN__RECORD'")
                    if not cursor.fetchone():
                        self.log_viewer.add_log("警告", f"文件 {file_name} 不包含KAO_QIN__RECORD表")
                        conn.close()
                        continue
                    
                    # 查询记录，按时间倒序排列
                    cursor.execute("""
                        SELECT _id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM 
                        FROM KAO_QIN__RECORD 
                        ORDER BY ENTER_TIME DESC
                    """)
                    
                    records = cursor.fetchall()
                    
                    # 将文件名添加到每条记录中
                    records_with_filename = [(file_name,) + record for record in records]
                    all_records.extend(records_with_filename)
                    
                    conn.close()
                    self.log_viewer.add_log("信息", f"已加载文件: {file_name}，共 {len(records)} 条记录")
                    
                except sqlite3.Error as e:
                    self.log_viewer.add_log("错误", f"数据库错误 ({file_name}): {str(e)}")
                    continue
                    
                except Exception as e:
                    self.log_viewer.add_log("错误", f"加载文件 {file_name} 失败: {str(e)}")
                    continue
            
            # 按时间倒序排序所有记录
            all_records.sort(key=lambda x: x[5] if x[5] else 0, reverse=True)
            
            # 限制显示100条记录
            all_records = all_records[:100]
            
            # 设置表格列数和标题 - 新的4列结构
            self.attendance_table.setColumnCount(4)
            self.attendance_table.setHorizontalHeaderLabels(["姓名", "人员编号", "组别", "打卡时间"])
            
            # 设置行数
            self.attendance_table.setRowCount(len(all_records))
            
            # 填充表格 - 新的4列结构
            for row, record in enumerate(all_records):
                # 第1列：姓名（原record[2]，因为record[0]是文件名，record[1]是ID）
                self.attendance_table.setItem(row, 0, QTableWidgetItem(str(record[2] or "")))
                # 第2列：人员编号（原record[3] - 身份证号）
                self.attendance_table.setItem(row, 1, QTableWidgetItem(str(record[3] or "")))
                # 第3列：组别（原record[4]）
                self.attendance_table.setItem(row, 2, QTableWidgetItem(str(record[4] or "")))
                # 第4列：打卡时间（原record[5]）
                timestamp = record[5]
                time_str = timestamp_to_beijing_string(timestamp) if timestamp else ""
                self.attendance_table.setItem(row, 3, QTableWidgetItem(time_str))
            
            # 设置列宽自动调整
            header = self.attendance_table.horizontalHeader()
            for i in range(4):
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
            
            if not all_records:
                self.log_viewer.add_log("提示", "没有找到任何考勤记录")
            else:
                self.log_viewer.add_log("信息", f"共加载 {len(all_records)} 条考勤记录")
                
        except Exception as e:
            self.log_viewer.add_log("错误", f"处理temp文件出错: {str(e)}")
            print(f"处理temp文件错误: {str(e)}")

    def update_sync_button_state(self, enabled: bool, text: str):
        """更新同步按钮状态"""
        self.log_viewer.add_log("调试", f"收到按钮状态更新信号: enabled={enabled}, text='{text}'")
        self.sync_button.setEnabled(enabled)
        self.sync_button.setText(text)

        # 如果按钮被启用且文本是"同步"，说明同步完成，释放锁定
        if enabled and text == "同步":
            self.sync_in_progress = False
            self.sync_lock = False

        self.log_viewer.add_log("信息", f"同步按钮状态已更新: {text} ({'启用' if enabled else '禁用'})")

    def on_sync_completed(self):
        """同步完成处理"""
        self.log_viewer.add_log("调试", "收到同步完成信号")
        self.log_viewer.add_log("信息", "同步完成，开始处理接收到的文件")

        try:
            # 1. 将temp目录中的所有文件复制到temp_bak目录
            self.log_viewer.add_log("调试", "开始复制文件到temp_bak目录")
            self.copy_temp_files_to_backup()

            # 2. 解析temp_bak目录中的.db文件并显示
            self.log_viewer.add_log("调试", "开始处理temp_bak文件")
            self.process_temp_bak_files()

            self.log_viewer.add_log("信息", "同步完成处理结束")

        except Exception as e:
            self.log_viewer.add_log("错误", f"同步完成处理失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def copy_temp_files_to_backup(self):
        """将temp目录中的所有文件复制到temp_bak目录"""
        import shutil

        try:
            from config.path_manager import get_temp_dir, get_temp_bak_dir, list_temp_files
            temp_dir = get_temp_dir()
            temp_bak_dir = get_temp_bak_dir()

            # 检查temp目录是否存在
            if not temp_dir.exists():
                self.log_viewer.add_log("提示", "temp目录不存在，跳过文件复制")
                return

            # 获取temp目录中的所有文件
            all_files = list_temp_files()
            if not all_files:
                self.log_viewer.add_log("提示", "temp目录为空，跳过文件复制")
                return

            # 确保temp_bak目录存在
            temp_bak_dir.mkdir(parents=True, exist_ok=True)

            # 复制文件到temp_bak目录
            copied_count = 0
            for file_path in all_files:
                try:
                    dest_path = temp_bak_dir / file_path.name

                    # 如果目标文件已存在，先删除（实现覆盖）
                    if dest_path.exists():
                        dest_path.unlink()
                        self.log_viewer.add_log("信息", f"覆盖已存在的文件: {file_path.name}")

                    # 复制文件
                    shutil.copy2(file_path, dest_path)
                    copied_count += 1
                    self.log_viewer.add_log("信息", f"已复制文件: {file_path.name}")

                except Exception as e:
                    self.log_viewer.add_log("错误", f"复制文件 {file_path.name} 失败: {str(e)}")
                    continue

            if copied_count > 0:
                self.log_viewer.add_log("信息", f"成功复制 {copied_count} 个文件到temp_bak目录")
            else:
                self.log_viewer.add_log("提示", "没有文件被复制")

        except Exception as e:
            self.log_viewer.add_log("错误", f"复制文件到temp_bak失败: {str(e)}")
            raise e

    def process_temp_bak_files(self):
        """处理temp_bak目录中的.db文件并显示考勤记录"""
        try:
            from config.path_manager import get_temp_bak_dir, list_temp_bak_files

            # 获取temp_bak目录中的所有.db文件
            temp_bak_dir = get_temp_bak_dir()
            if not temp_bak_dir.exists():
                self.log_viewer.add_log("提示", "temp_bak目录不存在")
                return

            # 获取所有.db文件
            db_files = list_temp_bak_files("*.db")
            if not db_files:
                self.log_viewer.add_log("提示", "temp_bak目录中没有找到.db文件")
                return

            self.log_viewer.add_log("信息", f"开始解析temp_bak目录中的 {len(db_files)} 个.db文件")

            # 使用现有的考勤记录数据展示接口
            self.attendance_table.load_temp_bak_records()

            # 统计信息
            total_records = self.attendance_table.rowCount()
            if total_records > 0:
                self.log_viewer.add_log("信息", f"数据解析完成：从 {len(db_files)} 个数据库文件加载了 {total_records} 条考勤记录")
            else:
                self.log_viewer.add_log("提示", "未找到任何有效的考勤记录")

        except Exception as e:
            self.log_viewer.add_log("错误", f"处理temp_bak文件失败: {str(e)}")
            import traceback
            traceback.print_exc()



    def on_export_clicked(self):
        """导出按钮点击事件 - 将考勤记录导出为Excel或CSV文件"""
        import sqlite3
        from datetime import datetime, timedelta
        from pathlib import Path
        from desktop.ui.export_dialog import ExportDialog

        try:
            # 显示导出设置对话框
            dialog = ExportDialog(self)
            if dialog.exec() != ExportDialog.DialogCode.Accepted:
                self.log_viewer.add_log("提示", "导出已取消")
                return

            # 获取导出设置
            export_settings = dialog.get_export_settings()

            # 获取temp_bak目录路径（修改：从temp改为temp_bak）
            from config.path_manager import get_temp_bak_dir, list_temp_bak_files
            temp_bak_dir = get_temp_bak_dir()
            if not temp_bak_dir.exists():
                self.log_viewer.add_log("错误", "temp_bak目录不存在")
                QMessageBox.warning(self, "导出失败", "temp_bak目录不存在，无法导出考勤记录")
                return

            # 获取temp_bak目录下的所有.db文件（修改：添加文件过滤逻辑）
            all_files = list_temp_bak_files("*.db")
            if not all_files:
                self.log_viewer.add_log("提示", "temp_bak目录下没有找到任何.db文件")
                QMessageBox.warning(self, "导出失败", "temp_bak目录下没有找到任何.db文件")
                return

            self.log_viewer.add_log("信息", f"开始导出，发现 {len(all_files)} 个.db文件: {[f.name for f in all_files]}")

            # 创建一个合并的记录列表
            all_records = []
            processed_files = 0
            total_records_from_all_files = 0
            
            # 从每个文件中读取记录
            for file_path in all_files:
                file_name = file_path.name
                conn = None  # 初始化连接变量

                try:
                    # 尝试作为SQLite数据库打开文件
                    conn = sqlite3.connect(str(file_path))
                    cursor = conn.cursor()

                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='KAO_QIN__RECORD'")
                    if not cursor.fetchone():
                        self.log_viewer.add_log("警告", f"文件 {file_name} 不包含KAO_QIN__RECORD表")
                        continue
                    
                    # 根据时间筛选设置构建查询
                    if export_settings["use_time_filter"]:
                        # 将日期转换为时间戳（毫秒）
                        from desktop.utils.time_utils import BEIJING_TZ
                        start_datetime = datetime.strptime(export_settings["start_date"], "%Y-%m-%d")
                        start_datetime = start_datetime.replace(tzinfo=BEIJING_TZ)
                        start_timestamp = int(start_datetime.timestamp() * 1000)

                        end_datetime = datetime.strptime(export_settings["end_date"], "%Y-%m-%d") + timedelta(days=1)
                        end_datetime = end_datetime.replace(tzinfo=BEIJING_TZ)
                        end_timestamp = int(end_datetime.timestamp() * 1000)

                        # 带时间筛选的查询
                        cursor.execute("""
                            SELECT _id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM
                            FROM KAO_QIN__RECORD
                            WHERE ENTER_TIME >= ? AND ENTER_TIME < ?
                            ORDER BY ENTER_TIME DESC
                        """, (start_timestamp, end_timestamp))
                    else:
                        # 查询所有记录
                        cursor.execute("""
                            SELECT _id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM
                            FROM KAO_QIN__RECORD
                            ORDER BY ENTER_TIME DESC
                        """)
                    
                    records = cursor.fetchall()

                    # 将文件名添加到每条记录中
                    records_with_filename = [(file_name,) + record for record in records]
                    all_records.extend(records_with_filename)
                    total_records_from_all_files += len(records)
                    processed_files += 1

                    self.log_viewer.add_log("信息", f"已从temp_bak目录加载文件: {file_name}，共 {len(records)} 条记录")

                except sqlite3.Error as e:
                    self.log_viewer.add_log("错误", f"数据库错误 ({file_name}): {str(e)}")
                    continue

                except Exception as e:
                    self.log_viewer.add_log("错误", f"加载文件 {file_name} 失败: {str(e)}")
                    continue

                finally:
                    # 确保数据库连接被正确关闭
                    if conn:
                        try:
                            conn.close()
                        except Exception as e:
                            self.log_viewer.add_log("警告", f"关闭数据库连接失败 ({file_name}): {str(e)}")
            
            # 记录详细的处理统计
            if export_settings["use_time_filter"]:
                self.log_viewer.add_log("信息", f"文件处理完成: {processed_files}/{len(all_files)} 个文件成功处理")
                self.log_viewer.add_log("信息", f"时间筛选后总计: {total_records_from_all_files} 条记录")
            else:
                self.log_viewer.add_log("信息", f"文件处理完成: {processed_files}/{len(all_files)} 个文件成功处理，总计 {total_records_from_all_files} 条记录")

            if not all_records:
                self.log_viewer.add_log("提示", "没有找到任何考勤记录")
                QMessageBox.warning(self, "导出失败", "没有找到任何考勤记录")
                return
                
            # 创建DataFrame
            columns = ["文件名", "ID", "姓名", "用户编号", "组别", "打卡时间", "MD5"]
            df = pd.DataFrame(all_records, columns=columns)
            
            # 转换时间戳为可读格式和日期（使用UTC+8时区）
            def convert_timestamp(ts):
                return parse_timestamp_for_export(ts)
            
            # 转换时间戳并提取日期和月份
            time_data = df["打卡时间"].apply(convert_timestamp)
            df["打卡时间"] = time_data.apply(lambda x: x["datetime"])
            df["日期"] = time_data.apply(lambda x: x["date"])
            df["月份"] = time_data.apply(lambda x: x["month"])
            
            # 按用户编号和日期分组，处理同一天多个打卡记录
            grouped = df.groupby(["用户编号", "姓名", "日期", "月份"])
            
            # 创建新的DataFrame用于导出
            export_data = []
            for (user_id, name, date, month), group in grouped:
                first_record = group.iloc[0]
                record = {
                    "用户编号": user_id,
                    "姓名": name,
                    "日期": date,
                    "组别": first_record["组别"],
                    "月份": month
                }
                
                # 将同一天多个打卡时间分开为多列，只保留时分秒
                times = group["打卡时间"].tolist()
                for i, time_str in enumerate(times, 1):
                    # 提取时间部分（时:分:秒）
                    try:
                        # 尝试解析日期时间字符串
                        if ' ' in time_str:  # 如果包含日期和时间
                            time_part = time_str.split(' ')[1]  # 提取时间部分
                        else:  # 如果只有时间
                            time_part = time_str
                            
                        # 确保时间格式为 HH:MM:SS
                        if len(time_part.split(':')) == 2:  # 如果只有时分
                            time_part += ":00"  # 添加秒
                            
                        record[f"打卡时间{i}"] = time_part
                    except Exception:
                        # 如果解析失败，保留原始值
                        record[f"打卡时间{i}"] = time_str
                
                export_data.append(record)
            
            # 创建最终的导出DataFrame
            df = pd.DataFrame(export_data)
            
            # 重新排序列，将组别放在最前面
            cols = ["组别", "月份", "用户编号", "姓名", "日期"]
            time_cols = [c for c in df.columns if c.startswith("打卡时间")]
            cols.extend(time_cols)
            df = df[cols]
            
            # 按月份和用户编号排序
            df = df.sort_values(["月份", "用户编号", "日期"], ascending=[False, True, False])

            # 记录时间筛选信息
            if export_settings["use_time_filter"]:
                time_range_info = f"{export_settings['start_date']} 至 {export_settings['end_date']}"
                self.log_viewer.add_log("信息", f"时间筛选: {export_settings['time_option']} ({time_range_info})")
            else:
                self.log_viewer.add_log("信息", f"时间筛选: {export_settings['time_option']} - 导出所有记录")
            
            # 根据数据选项筛选列
            if not export_settings["include_filename"]:
                df = df.drop(columns=["文件名"])
            
            if df.empty:
                self.log_viewer.add_log("提示", "筛选后没有符合条件的记录")
                QMessageBox.warning(self, "导出失败", "筛选后没有符合条件的记录")
                return
            
            # 让用户选择保存位置（使用北京时间）
            current_date = get_current_date_string()
            default_filename = f"考勤记录_{current_date}{export_settings['format']}"
            
            file_filter = "Excel文件 (*.xlsx)" if export_settings["format"] == ".xlsx" else "CSV文件 (*.csv)"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存考勤记录", default_filename, file_filter
            )
            
            if not file_path:
                self.log_viewer.add_log("提示", "导出已取消")
                return
            
            # 根据文件格式导出 - 添加异常处理
            try:
                if export_settings["format"] == ".xlsx":
                    # 导出并设置列宽
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        df.to_excel(writer, sheet_name='考勤记录', index=False)

                        # 动态调整列宽 - 添加异常处理
                        try:
                            worksheet = writer.sheets['考勤记录']
                            worksheet.column_dimensions['A'].width = 10  # 组别
                            worksheet.column_dimensions['B'].width = 12  # 月份
                            worksheet.column_dimensions['C'].width = 15  # 用户编号
                            worksheet.column_dimensions['D'].width = 10  # 姓名
                            worksheet.column_dimensions['E'].width = 12  # 日期

                            # 打卡时间列动态设置
                            time_cols = [c for c in df.columns if c.startswith("打卡时间")]
                            for i, col in enumerate(time_cols, 1):
                                col_letter = chr(69+i)  # 从F列开始（E是日期）
                                worksheet.column_dimensions[col_letter].width = 12  # 打卡时间1,2,3...

                                # 设置时间格式
                                for row_idx, cell in enumerate(worksheet[col_letter][1:], 2):  # 从第2行开始（跳过标题）
                                    if cell.value:
                                        try:
                                            # 尝试设置时间格式
                                            cell.number_format = 'hh:mm:ss'
                                        except:
                                            pass  # 如果设置失败，保持默认格式
                        except Exception as e:
                            self.log_viewer.add_log("警告", f"设置Excel格式失败: {str(e)}")
                else:
                    df.to_csv(file_path, index=False, encoding="utf-8-sig")  # 使用带BOM的UTF-8编码，以便Excel正确显示中文

            except PermissionError:
                self.log_viewer.add_log("错误", f"文件被占用或权限不足: {file_path}")
                QMessageBox.critical(self, "导出失败", f"文件被占用或权限不足，请关闭相关程序后重试:\n{file_path}")
                return
            except Exception as e:
                self.log_viewer.add_log("错误", f"文件写入失败: {str(e)}")
                QMessageBox.critical(self, "导出失败", f"文件写入失败: {str(e)}")
                return
            
            # 详细的导出成功信息
            export_summary = f"导出完成: {len(df)} 条考勤记录"
            if export_settings["use_time_filter"]:
                export_summary += f" (时间范围: {export_settings['time_option']})"
            else:
                export_summary += " (全部记录)"

            self.log_viewer.add_log("信息", f"{export_summary} → {file_path}")
            QMessageBox.information(self, "导出成功", export_summary)
                
        except Exception as e:
            self.log_viewer.add_log("错误", f"导出过程出错: {str(e)}")
            QMessageBox.critical(self, "导出失败", f"导出过程出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def update_attendance_data(self):
        """更新考勤数据（手动调用或定时器调用）- 增强异常处理"""
        try:
            # 检查是否正在关闭程序
            if not hasattr(self, 'attendance_table') or not self.attendance_table:
                return

            from config.path_manager import list_temp_bak_files

            # 检查数据源（静默检查，不记录详细日志）
            all_db_files = list_temp_bak_files("*.db")

            # 加载temp_bak目录中的考勤记录
            old_count = self.attendance_table.rowCount()
            self.attendance_table.load_temp_bak_records()
            new_count = self.attendance_table.rowCount()

            # 只在数据有变化时记录日志
            if new_count != old_count:
                self.log_viewer.add_log("信息", f"数据更新：从 {len(all_db_files)} 个文件加载了 {new_count} 条记录（变化：{new_count - old_count}）")

        except Exception as e:
            # 增强异常处理，避免崩溃
            if hasattr(self, 'log_viewer') and self.log_viewer:
                self.log_viewer.add_log("错误", f"更新考勤数据失败: {str(e)}")
            else:
                print(f"更新考勤数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def start_server_monitoring(self, log_file_path: str):
        """开始服务端监控"""
        try:
            self.server_monitor.start_monitoring(log_file_path)
            self.log_viewer.add_log("信息", f"开始监控服务端日志: {log_file_path}")
        except Exception as e:
            self.log_viewer.add_log("错误", f"启动服务端监控失败: {str(e)}")

    def stop_server_monitoring(self):
        """停止服务端监控"""
        try:
            self.server_monitor.stop_monitoring()
            self.log_viewer.add_log("信息", "已停止服务端监控")
        except Exception as e:
            self.log_viewer.add_log("错误", f"停止服务端监控失败: {str(e)}")

    def on_server_monitoring_error(self, error_message: str):
        """处理服务端监控错误"""
        self.server_log_viewer.add_log_entry(type('LogEntry', (), {
            'timestamp': create_log_entry_timestamp(),
            'level': 'ERROR',
            'logger_name': 'monitor',
            'message': f"监控错误: {error_message}"
        })())

    def start_udp_broadcast_service(self):
        """启动UDP广播服务"""
        try:
            # 初始化UDP广播服务（默认服务端口8000，广播端口37020）
            self.udp_broadcast_service = UDPBroadcastService(
                server_port=8000,
                broadcast_port=37020,
                broadcast_interval=5.0
            )

            # 设置回调函数
            self.udp_broadcast_service.set_callbacks(
                on_start=self._on_udp_broadcast_start,
                on_stop=self._on_udp_broadcast_stop,
                on_error=self._on_udp_broadcast_error
            )

            # 启动服务
            success = self.udp_broadcast_service.start()
            if not success:
                self.log_viewer.add_log("错误", "UDP广播服务启动失败")

        except Exception as e:
            self.log_viewer.add_log("错误", f"初始化UDP广播服务失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def stop_udp_broadcast_service(self):
        """停止UDP广播服务"""
        try:
            if hasattr(self, 'udp_broadcast_service') and self.udp_broadcast_service:
                self.udp_broadcast_service.stop()
                self.udp_broadcast_service = None
        except Exception as e:
            self.log_viewer.add_log("错误", f"停止UDP广播服务失败: {str(e)}")

    def get_preferred_ip_address(self):
        """获取优先的IP地址"""
        try:
            from .utils.network_utils import get_preferred_ip_address
            return get_preferred_ip_address()
        except Exception as e:
            self.log_viewer.add_log("错误", f"获取优先IP地址失败: {str(e)}")
            return None

    def _on_udp_broadcast_start(self, server_ip: str, broadcast_port: int):
        """UDP广播服务启动回调"""
        self.log_viewer.add_log("信息", f"启动UDP广播服务，端口: {broadcast_port}")
        self.log_viewer.add_log("信息", f"选择广播IP地址: {server_ip}:8000")
        self.log_viewer.add_log("信息", "UDP广播服务启动成功")

    def _on_udp_broadcast_stop(self):
        """UDP广播服务停止回调"""
        self.log_viewer.add_log("信息", "UDP广播服务已停止")

    def _on_udp_broadcast_error(self, error_message: str):
        """UDP广播服务错误回调"""
        self.log_viewer.add_log("错误", f"UDP广播服务错误: {error_message}")









    def closeEvent(self, event):
        """窗口关闭事件 - 增强资源清理"""
        try:
            self.log_viewer.add_log("信息", "开始关闭应用程序...")

            # 1. 首先停止定时器，避免在清理过程中继续执行
            if hasattr(self, 'data_timer') and self.data_timer:
                try:
                    self.data_timer.stop()
                    self.log_viewer.add_log("信息", "已停止数据更新定时器")
                except Exception as e:
                    print(f"停止定时器失败: {e}")

            # 2. 停止异步初始化工作线程
            if hasattr(self, 'async_init_worker') and self.async_init_worker and self.async_init_worker.isRunning():
                try:
                    self.async_init_worker.quit()
                    if not self.async_init_worker.wait(3000):  # 等待最多3秒
                        self.async_init_worker.terminate()  # 强制终止
                        self.async_init_worker.wait(1000)  # 再等待1秒
                    self.log_viewer.add_log("信息", "已停止异步初始化线程")
                except Exception as e:
                    print(f"停止异步初始化线程失败: {e}")

            # 3. 停止temp目录监控
            if hasattr(self, 'sync_controller_enhanced') and self.sync_controller_enhanced:
                try:
                    self.sync_controller_enhanced.stop_monitoring()
                    self.log_viewer.add_log("信息", "已停止temp目录监控")
                except Exception as e:
                    print(f"停止temp目录监控失败: {e}")

            # 4. 停止UDP广播服务
            try:
                self.stop_udp_broadcast_service()
            except Exception as e:
                print(f"停止UDP广播服务失败: {e}")

            # 5. 停止服务端监控
            try:
                self.stop_server_monitoring()
            except Exception as e:
                print(f"停止服务端监控失败: {e}")

            self.log_viewer.add_log("信息", "应用程序关闭完成")

        except Exception as e:
            print(f"关闭应用程序时发生错误: {e}")
            import traceback
            traceback.print_exc()

        event.accept()

def setup_localization():
    """设置本地化"""
    # 设置系统环境
    setup_environment_variables()
    setup_system_locale()
    
    # 设置默认语言为中文
    locale = QLocale(QLocale.Language.Chinese, QLocale.Country.China)
    QLocale.setDefault(locale)
    
    # 创建翻译器
    translator = QTranslator()
    
    # 尝试加载翻译文件（如果存在）
    # translator.load("kao_qin_zh_CN", "translations")
    
    return translator

def main():
    import sys
    from PyQt6.QtWidgets import QApplication
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("考勤系统管理端")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("考勤系统")
    
    # 设置本地化
    translator = setup_localization()
    app.installTranslator(translator)
    
    # 设置默认字体
    font = QFont(get_font_family(), get_font_size())
    app.setFont(font)
    
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 