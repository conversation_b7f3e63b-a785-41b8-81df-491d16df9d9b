"""
Configuration settings for the attendance server application.
"""
import os
import sys
from pathlib import Path

# 导入路径管理器
try:
    from config.path_manager import get_path_manager
    path_manager = get_path_manager()
    USE_PATH_MANAGER = True
except ImportError:
    # 如果无法导入路径管理器，使用备用方案
    USE_PATH_MANAGER = False
    print("[服务端配置] 警告: 无法导入路径管理器，使用备用路径配置")

# Base directory of the project
def get_base_dir():
    """获取基础目录，兼容开发环境和打包环境"""
    if USE_PATH_MANAGER:
        return path_manager.get_app_root()

    # 备用方案
    if getattr(sys, 'frozen', False):
        # 打包环境：使用可执行文件所在目录
        if 'ATTENDANCE_APP_PATH' in os.environ:
            return Path(os.environ['ATTENDANCE_APP_PATH'])
        else:
            return Path(sys.executable).parent
    else:
        # 开发环境：使用项目根目录
        return Path(__file__).parent.parent

BASE_DIR = get_base_dir()

# Database settings
def get_database_config():
    """获取数据库配置"""
    if USE_PATH_MANAGER:
        return {
            'path': str(path_manager.get_app_root() / 'attendance_server.db'),
            'backup_dir': str(path_manager.get_app_root() / 'backups'),
        }
    else:
        return {
            'path': str(BASE_DIR / 'attendance_server.db'),
            'backup_dir': str(BASE_DIR / 'backups'),
        }

DATABASE = get_database_config()

# API settings
API = {
    'host': '0.0.0.0',
    'port': 8000,
    'prefix': '/api',
    'debug': True,
}

# Device settings
DEVICE = {
    'heartbeat_timeout': 30,  # seconds
    'sync_timeout': 300,  # seconds
    'upload_max_size': 50 * 1024 * 1024,  # 50MB
}

# Logging settings
def get_log_file_path():
    """获取日志文件路径，兼容开发环境和打包环境"""
    if USE_PATH_MANAGER:
        # 使用路径管理器
        log_file_path = path_manager.get_server_log_file_path('server.log')
        print(f"[服务端配置] 使用路径管理器获取日志路径: {log_file_path}")
        return str(log_file_path)

    # 备用方案
    if getattr(sys, 'frozen', False):
        # 打包环境：使用统一的日志目录
        if 'ATTENDANCE_LOGS_DIR' in os.environ:
            logs_dir = Path(os.environ['ATTENDANCE_LOGS_DIR'])
        else:
            logs_dir = BASE_DIR / 'logs'
    else:
        # 开发环境：使用attendance_server目录下的logs
        logs_dir = Path(__file__).parent / 'logs'

    # 确保日志目录存在
    logs_dir.mkdir(parents=True, exist_ok=True)

    # 输出调试信息
    print(f"[服务端配置] 备用方案日志目录: {logs_dir}")
    print(f"[服务端配置] 日志文件: {logs_dir / 'server.log'}")

    return str(logs_dir / 'server.log')

LOGGING = {
    'level': 'DEBUG',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': get_log_file_path(),
}

# Create necessary directories
def ensure_directories():
    """确保必要的目录存在"""
    try:
        # 创建数据库备份目录
        backup_dir = DATABASE['backup_dir']
        os.makedirs(backup_dir, exist_ok=True)
        print(f"[服务端配置] 数据库备份目录: {backup_dir}")

        # 日志目录已在get_log_file_path()中创建

        # 如果使用路径管理器，确保所有目录都存在
        if USE_PATH_MANAGER:
            # 路径管理器会自动创建必要的目录
            print(f"[服务端配置] 使用路径管理器管理目录")
        else:
            # 备用方案：在打包环境中，确保服务端日志目录存在
            if getattr(sys, 'frozen', False):
                if 'ATTENDANCE_LOGS_DIR' in os.environ:
                    server_logs_dir = Path(os.environ['ATTENDANCE_LOGS_DIR'])
                    server_logs_dir.mkdir(parents=True, exist_ok=True)
                    print(f"[服务端配置] 服务端日志目录: {server_logs_dir}")

    except Exception as e:
        print(f"[服务端配置] 创建目录时出错: {e}")

# 初始化目录
ensure_directories()