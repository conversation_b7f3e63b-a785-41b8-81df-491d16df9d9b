import sqlite3
from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from typing import List, Optional

from .models import Device, AttendanceRecord, SyncLog
from ..config import DEVICE

DB_PATH = 'attendance_server.db'

def get_db_connection():
    return sqlite3.connect(DB_PATH)

def get_devices():
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute('SELECT device_id, last_heartbeat, online, last_sync, sync_status FROM devices')
    rows = cur.fetchall()
    conn.close()
    return [{"deviceId": r[0], "lastHeartbeat": r[1], "online": bool(r[2]), "lastSync": r[3], "syncStatus": r[4]} for r in rows]

def add_device(device):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute('INSERT INTO devices (device_id, online) VALUES (?, ?)', (device['deviceId'], 0))
        conn.commit()
        return {"success": True}
    except sqlite3.IntegrityError:
        raise HTTPException(status_code=400, detail="Device already exists")
    finally:
        conn.close()

def update_device(device_id, device):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute('UPDATE devices SET last_sync=?, sync_status=? WHERE device_id=?',
                   (device.get('lastSync'), device.get('syncStatus'), device_id))
        conn.commit()
        return {"success": True}
    finally:
        conn.close()

def delete_device(device_id):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute('DELETE FROM devices WHERE device_id=?', (device_id,))
        conn.commit()
        return {"success": True}
    finally:
        conn.close()

def update_heartbeat(device_id):
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute('UPDATE devices SET last_heartbeat=datetime("now"), online=1 WHERE device_id=?', (device_id,))
        conn.commit()
    finally:
        conn.close()

def get_attendance_records(device_id=None, date=None, user=None):
    conn = get_db_connection()
    cur = conn.cursor()
    sql = 'SELECT device_id, sfz_name, sfz_id, sfz_group, enter_time, md5sum FROM attendance_records WHERE 1=1'
    params = []
    if device_id:
        sql += ' AND device_id=?'
        params.append(device_id)
    if date:
        sql += ' AND enter_time LIKE ?'
        params.append(f'{date}%')
    if user:
        sql += ' AND sfz_name=?'
        params.append(user)
    cur.execute(sql, params)
    rows = cur.fetchall()
    conn.close()
    return [{"deviceId": r[0], "sfzName": r[1], "sfzId": r[2], "sfzGroup": r[3], "enterTime": r[4], "md5sum": r[5]} for r in rows]

def get_logs(level=None, limit=100):
    conn = get_db_connection()
    cur = conn.cursor()
    sql = 'SELECT time, level, msg FROM logs WHERE 1=1'
    params = []
    if level:
        sql += ' AND level=?'
        params.append(level)
    sql += ' ORDER BY time DESC LIMIT ?'
    params.append(limit)
    cur.execute(sql, params)
    rows = cur.fetchall()
    conn.close()
    return [{"time": r[0], "level": r[1], "msg": r[2]} for r in rows]

def set_sync_request(device_id):
    """检查设备是否需要同步"""
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute('SELECT sync_status FROM devices WHERE device_id=?', (device_id,))
        row = cur.fetchone()
        if row and row[0] == 'pending':
            cur.execute('UPDATE devices SET sync_status="requested" WHERE device_id=?', (device_id,))
            conn.commit()
            return 102  # 请求同步
        return 101  # 正常心跳
    finally:
        conn.close()

def import_kaoqin_db(file_path, device_id):
    """导入考勤数据库"""
    src_conn = sqlite3.connect(f'file:{file_path}?mode=ro', uri=True)
    src_cur = src_conn.cursor()
    dst_conn = get_db_connection()
    dst_cur = dst_conn.cursor()
    
    try:
        # 读取考勤记录
        src_cur.execute('SELECT sfz_name, sfz_id, sfz_group, enter_time, md5sum FROM Kao_qin_Record')
        records = src_cur.fetchall()
        
        # 批量插入
        for record in records:
            dst_cur.execute('''
                INSERT OR IGNORE INTO attendance_records 
                (device_id, sfz_name, sfz_id, sfz_group, enter_time, md5sum)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (device_id,) + record)
        
        # 更新设备同步状态
        dst_cur.execute('''
            UPDATE devices 
            SET last_sync=datetime('now'), sync_status='completed'
            WHERE device_id=?
        ''', (device_id,))
        
        dst_conn.commit()
        return {"success": True, "imported": len(records)}
        
    except Exception as e:
        dst_conn.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        src_conn.close()
        dst_conn.close()

def update_device_status(device_id: str, status: str, timestamp: str):
    """更新设备状态"""
    conn = get_db_connection()
    cur = conn.cursor()
    try:
        cur.execute('''
            INSERT INTO devices (device_id, status, last_heartbeat) 
            VALUES (?, ?, ?)
            ON CONFLICT(device_id) DO UPDATE SET 
            status=excluded.status, 
            last_heartbeat=excluded.last_heartbeat
        ''', (device_id, status, timestamp))
        conn.commit()
    finally:
        conn.close()

def get_device(db: Session, device_id: str) -> Optional[Device]:
    """Get a device by its device_id."""
    return db.query(Device).filter(Device.device_id == device_id).first()

def create_device(db: Session, device_id: str, name: str = None) -> Device:
    """Create a new device if it doesn't exist."""
    device = Device(
        device_id=device_id,
        name=name or device_id,
        is_online=True,
        last_heartbeat=datetime.utcnow()
    )
    db.add(device)
    db.commit()
    db.refresh(device)
    return device

def update_device_heartbeat(db: Session, device_id: str) -> Device:
    """Update device's heartbeat timestamp and online status."""
    device = get_device(db, device_id)
    if not device:
        return create_device(db, device_id)
    
    device.last_heartbeat = datetime.utcnow()
    device.is_online = True
    db.commit()
    db.refresh(device)
    return device

def update_device_sync_status(db: Session, device_id: str, status: str) -> Optional[Device]:
    """Update device's sync status."""
    device = get_device(db, device_id)
    if device:
        device.sync_status = status
        db.commit()
        db.refresh(device)
    return device

def get_all_devices(db: Session) -> List[Device]:
    """Get all devices and update their online status based on heartbeat."""
    devices = db.query(Device).all()
    timeout = datetime.utcnow() - timedelta(seconds=DEVICE['heartbeat_timeout'])
    
    for device in devices:
        if device.last_heartbeat and device.last_heartbeat < timeout:
            device.is_online = False
    
    db.commit()
    return devices

def create_attendance_records(db: Session, records: List[dict], device_id: str) -> int:
    """Batch create attendance records."""
    try:
        new_records = [
            AttendanceRecord(
                device_id=device_id,
                sfz_id=record['sfz_id'],
                name=record.get('name'),
                enter_time=record['enter_time'],
                md5sum=record['md5sum']
            )
            for record in records
        ]
        db.bulk_save_objects(new_records)
        db.commit()
        return len(new_records)
    except SQLAlchemyError as e:
        db.rollback()
        raise e

def create_sync_log(db: Session, device_id: str, file_name: str, file_size: int) -> SyncLog:
    """Create a new sync log entry."""
    log = SyncLog(
        device_id=device_id,
        file_name=file_name,
        file_size=file_size,
        status='started'
    )
    db.add(log)
    db.commit()
    db.refresh(log)
    return log

def update_sync_log(db: Session, log_id: int, status: str, records_count: int = None,
                   error_message: str = None) -> Optional[SyncLog]:
    """Update sync log status and details."""
    log = db.query(SyncLog).filter(SyncLog.id == log_id).first()
    if log:
        log.status = status
        log.completed_at = datetime.utcnow()
        if records_count is not None:
            log.records_count = records_count
        if error_message:
            log.error_message = error_message
        db.commit()
        db.refresh(log)
    return log

def get_attendance_records(db: Session, device_id: str = None,
                         start_date: datetime = None,
                         end_date: datetime = None) -> List[AttendanceRecord]:
    """Get attendance records with optional filters."""
    query = db.query(AttendanceRecord)
    
    if device_id:
        query = query.filter(AttendanceRecord.device_id == device_id)
    if start_date:
        query = query.filter(AttendanceRecord.enter_time >= start_date)
    if end_date:
        query = query.filter(AttendanceRecord.enter_time <= end_date)
    
    return query.order_by(AttendanceRecord.enter_time.desc()).all() 