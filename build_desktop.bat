@echo off
chcp 65001 > nul
echo ========================================
echo 考勤系统桌面程序打包脚本
echo ========================================

echo.
echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境
    pause
    exit /b 1
)

echo.
echo 检查虚拟环境...
if exist "venv\Scripts\activate.bat" (
    echo 激活虚拟环境...
    call venv\Scripts\activate.bat
) else (
    echo 警告: 未找到虚拟环境，使用系统Python
)

echo.
echo 检查构建依赖...
python -c "import PyInstaller" 2>nul
if %errorlevel% neq 0 (
    echo 安装PyInstaller...
    pip install pyinstaller>=6.0.0
)

echo.
echo 安装桌面程序依赖...
if exist "requirements.txt" (
    echo 安装项目依赖...
    pip install -r requirements.txt
) else (
    echo 安装基本依赖...
    pip install PyQt6 pandas openpyxl sqlalchemy pyinstaller
)

echo.
echo 开始构建桌面程序...
python build_desktop_app.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 🎉 构建成功！
    echo 📁 可执行文件位于: dist\AttendanceDesktop.exe
    echo 📦 分发包位于: dist\AttendanceDesktop_Package\
    echo ========================================
    echo.
    echo 使用方法:
    echo 1. 进入 dist\AttendanceDesktop_Package\ 目录
    echo 2. 双击 AttendanceDesktop.exe 启动程序
    echo 3. 程序会自动创建必要的工作目录
) else (
    echo.
    echo ========================================
    echo ❌ 构建失败！请检查错误信息
    echo ========================================
)

echo.
pause
