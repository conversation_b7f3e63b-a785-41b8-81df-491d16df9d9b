"""
Heartbeat API endpoint for device status management.
"""
import logging
from fastapi import APIRouter, Depends, HTTPException, UploadFile, Form, Response
from pydantic import BaseModel
from datetime import datetime
import os
from pathlib import Path
from sqlalchemy.orm import Session
from typing import Dict, Optional
import traceback
import shutil

from ..db.database import get_db
from ..db.crud import get_device, update_device_heartbeat, update_device_sync_status
from ..schemas.device import DeviceResponse

router = APIRouter()
logger = logging.getLogger(__name__)

# 设置更详细的日志格式
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 导入路径管理器
try:
    from config.path_manager import get_path_manager
    path_manager = get_path_manager()
    UPDATE_FILE_PATH = path_manager.get_config_file_path("update.txt")
    USE_PATH_MANAGER = True
except ImportError:
    # 备用方案：使用硬编码路径
    PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
    UPDATE_FILE_PATH = PROJECT_ROOT / "temp_cfg" / "update.txt"
    USE_PATH_MANAGER = False
    print("[心跳API] 警告: 无法导入路径管理器，使用备用路径配置")

class HeartbeatRequest(BaseModel):
    device_id: str
    timestamp: str
    status: str

def _check_and_handle_update_file() -> str:
    """
    检查 temp_cfg/update.txt 文件是否存在，如果存在则删除并返回 'upload'，否则返回默认消息

    Returns:
        str: 如果文件存在返回 'upload'，否则返回 'Heartbeat received'
    """
    try:
        if UPDATE_FILE_PATH.exists():
            logger.info(f"检测到更新文件存在: {UPDATE_FILE_PATH}")
            try:
                # 删除文件
                UPDATE_FILE_PATH.unlink()
                logger.info(f"成功删除更新文件: {UPDATE_FILE_PATH}")
                return "upload"
            except OSError as e:
                logger.error(f"删除更新文件失败: {UPDATE_FILE_PATH}, 错误: {e}")
                # 即使删除失败，也返回 upload 消息，因为文件确实存在
                return "upload"
        else:
            logger.debug(f"更新文件不存在: {UPDATE_FILE_PATH}")
            return "Heartbeat received"
    except Exception as e:
        logger.error(f"检查更新文件时发生错误: {e}", exc_info=True)
        # 发生异常时返回默认消息，确保心跳响应正常
        return "Heartbeat received"


@router.post("/heartbeat/{device_id}")
async def handle_heartbeat(device_id: str, request: HeartbeatRequest, db: Session = Depends(get_db)):
    """
    Handle device heartbeat request.
    Returns device status and sync request if pending.
    检查 temp_cfg/update.txt 文件，如果存在则在响应中设置 message 为 'upload' 并删除文件。
    """
    if device_id != request.device_id:
        raise HTTPException(
            status_code=400,
            detail="Device ID in path does not match device ID in request body."
        )
    try:
        device = update_device_heartbeat(db, device_id)
        if device is None:
            raise HTTPException(status_code=404, detail="Device not found")

        # 检查并处理更新文件
        message = _check_and_handle_update_file()

        logger.info(f"处理设备 {device_id} 心跳请求，响应消息: {message}")

        return {
            "device_id": device_id,
            "status": "success",
            "message": message,
            "sync_status": device.sync_status
        }

    except Exception as e:
        logger.error(f"Error processing heartbeat for {device_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An internal error occurred while processing the heartbeat."
        )

@router.post("/heartbeat/upload")
async def upload_kaoqin_db(
    file: UploadFile,
    device_id: str = Form(...),
    db: Session = Depends(get_db)
):
    """处理考勤数据库文件上传"""
    logger.info("="*80)
    logger.info("[开始] 文件上传处理")
    logger.info(f"[请求信息] device_id={device_id}")
    logger.info(f"[文件信息] filename={file.filename}, content_type={file.content_type}, size={file.size if hasattr(file, 'size') else 'unknown'}")
    
    try:
        # 检查设备
        logger.debug("[步骤1] 验证设备ID")
        device = get_device(db, device_id)
        if device is None:
            logger.error(f"[错误] 设备未找到: device_id={device_id}")
            raise HTTPException(status_code=404, detail="Device not found")
        logger.info("[成功] 设备验证通过")
        
        # 获取上传目录
        if USE_PATH_MANAGER:
            # 使用路径管理器获取uploads目录
            upload_dir = path_manager.get_uploads_dir()
            logger.debug(f"[步骤2] 使用路径管理器获取上传目录: {upload_dir}")
        else:
            # 备用方案：使用相对路径
            upload_dir = Path("uploads")
            upload_dir.mkdir(parents=True, exist_ok=True)
            logger.debug(f"[步骤2] 使用备用方案创建上传目录: {upload_dir}")

        try:
            logger.info(f"[成功] 上传目录已就绪: {upload_dir}")
        except Exception as e:
            logger.error(f"[错误] 创建目录失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to create upload directory: {str(e)}")

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        save_filename = f"{device_id}_{timestamp}.db"
        save_path = upload_dir / save_filename
        logger.debug(f"[步骤3] 准备保存文件: {save_path}")
        
        # 保存文件
        try:
            logger.debug("[步骤4] 开始保存文件...")
            with open(str(save_path), "wb") as buffer:
                # 读取文件内容
                content = await file.read()
                logger.debug(f"[文件内容] 读取到的数据大小: {len(content)} bytes")
                # 写入文件
                buffer.write(content)
                logger.info(f"[成功] 文件已保存: {save_path}")
            
            # 返回成功响应
            response_data = {
                "device_id": device_id,
                "status": "success",
                "message": f"File saved as {save_filename}",
                "sync_status": "received",
                "file_size": len(content)
            }
            logger.info(f"[返回] 响应数据: {response_data}")
            return response_data
            
        except Exception as save_error:
            error_msg = f"保存文件失败: {str(save_error)}"
            logger.error(f"[错误] {error_msg}")
            logger.error(f"[错误详情] {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=error_msg)
                
    except HTTPException as http_error:
        logger.error(f"[HTTP错误] {http_error.detail}")
        raise
    except Exception as e:
        error_msg = f"处理文件上传时发生错误: {str(e)}"
        logger.error(f"[系统错误] {error_msg}")
        logger.error(f"[错误堆栈] {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=error_msg)
    finally:
        logger.info("[结束] 文件上传处理完成")
        logger.info("="*80) 