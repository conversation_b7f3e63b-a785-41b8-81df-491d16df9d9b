#!/usr/bin/env python3
"""
验证新同步工作流实现的完整性
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_files_exist():
    """验证所有必要文件是否存在"""
    print("=== 验证文件存在性 ===")
    
    required_files = [
        "config/device_config.py",
        "desktop/utils/update_file_monitor.py", 
        "temp_cfg/dev_sum.txt",
        "NEW_SYNC_WORKFLOW_IMPLEMENTATION.md"
    ]
    
    modified_files = [
        "attendance_server/api/heartbeat.py",
        "desktop/main_window.py",
        "desktop/ui/attendance_table.py"
    ]
    
    all_exist = True
    
    print("新增文件:")
    for file_path in required_files:
        path = Path(file_path)
        exists = path.exists()
        status = "✅" if exists else "❌"
        print(f"  {status} {file_path}")
        if not exists:
            all_exist = False
    
    print("\n修改文件:")
    for file_path in modified_files:
        path = Path(file_path)
        exists = path.exists()
        status = "✅" if exists else "❌"
        print(f"  {status} {file_path}")
        if not exists:
            all_exist = False
    
    return all_exist

def verify_imports():
    """验证关键模块可以正常导入"""
    print("\n=== 验证模块导入 ===")
    
    imports_to_test = [
        ("config.device_config", "get_client_total"),
        ("desktop.utils.update_file_monitor", "SyncWorkflowController"),
        ("desktop.utils.update_file_monitor", "UpdateFileMonitor"),
    ]
    
    all_imported = True
    
    for module_name, item_name in imports_to_test:
        try:
            module = __import__(module_name, fromlist=[item_name])
            getattr(module, item_name)
            print(f"  ✅ {module_name}.{item_name}")
        except ImportError as e:
            print(f"  ❌ {module_name}.{item_name} - 导入错误: {e}")
            all_imported = False
        except AttributeError as e:
            print(f"  ❌ {module_name}.{item_name} - 属性错误: {e}")
            all_imported = False
        except Exception as e:
            print(f"  ❌ {module_name}.{item_name} - 其他错误: {e}")
            all_imported = False
    
    return all_imported

def verify_device_config():
    """验证设备配置功能"""
    print("\n=== 验证设备配置功能 ===")
    
    try:
        from config.device_config import get_client_total, set_client_total
        
        # 测试读取
        current_total = get_client_total()
        print(f"  当前客户端总数: {current_total}")
        
        # 测试设置
        test_value = 5
        success = set_client_total(test_value)
        if success:
            new_total = get_client_total()
            if new_total == test_value:
                print(f"  ✅ 设置功能正常: {test_value}")
            else:
                print(f"  ❌ 设置功能异常: 期望{test_value}, 实际{new_total}")
                return False
        else:
            print("  ❌ 设置功能失败")
            return False
        
        # 恢复原值
        set_client_total(current_total)
        print(f"  已恢复原值: {current_total}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 设备配置功能验证失败: {e}")
        return False

def verify_heartbeat_modifications():
    """验证心跳接口修改"""
    print("\n=== 验证心跳接口修改 ===")
    
    try:
        # 检查心跳文件中是否包含新的逻辑
        heartbeat_file = Path("attendance_server/api/heartbeat.py")
        content = heartbeat_file.read_text(encoding='utf-8')
        
        required_elements = [
            "from config.device_config import get_client_total",
            "def _check_and_handle_update_file(device_id: str)",
            "device_files = [name for name in file_names if name.startswith(device_id)]",
            "if total_files >= client_total:"
        ]
        
        all_found = True
        for element in required_elements:
            if element in content:
                print(f"  ✅ 找到: {element[:50]}...")
            else:
                print(f"  ❌ 未找到: {element[:50]}...")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"  ❌ 心跳接口修改验证失败: {e}")
        return False

def verify_desktop_modifications():
    """验证桌面程序修改"""
    print("\n=== 验证桌面程序修改 ===")
    
    try:
        # 检查主窗口文件
        main_window_file = Path("desktop/main_window.py")
        content = main_window_file.read_text(encoding='utf-8')
        
        required_elements = [
            "from .utils.update_file_monitor import SyncWorkflowController",
            "self.sync_workflow_controller = SyncWorkflowController(self)",
            "self.sync_workflow_controller.start_sync_workflow()",
            "self.sync_workflow_controller.cleanup()"
        ]
        
        all_found = True
        for element in required_elements:
            if element in content:
                print(f"  ✅ 主窗口: {element[:40]}...")
            else:
                print(f"  ❌ 主窗口: {element[:40]}...")
                all_found = False
        
        # 检查表格文件
        table_file = Path("desktop/ui/attendance_table.py")
        table_content = table_file.read_text(encoding='utf-8')
        
        if "max_display_for_sync = 100" in table_content:
            print("  ✅ 表格: 100条记录限制")
        else:
            print("  ❌ 表格: 100条记录限制")
            all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"  ❌ 桌面程序修改验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始验证新同步工作流实现")
    print("=" * 50)
    
    verification_results = []
    
    # 运行各项验证
    verification_results.append(("文件存在性", verify_files_exist()))
    verification_results.append(("模块导入", verify_imports()))
    verification_results.append(("设备配置功能", verify_device_config()))
    verification_results.append(("心跳接口修改", verify_heartbeat_modifications()))
    verification_results.append(("桌面程序修改", verify_desktop_modifications()))
    
    # 汇总验证结果
    print("\n" + "=" * 50)
    print("📋 验证结果汇总:")
    
    passed = 0
    total = len(verification_results)
    
    for verification_name, result in verification_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {verification_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("🎉 所有验证通过！新同步工作流实现完整且正确。")
        print("\n📝 实现总结:")
        print("  ✅ 功能修改1: 添加全局变量 - 客户端总数")
        print("  ✅ 功能修改2: 修改服务端心跳接口处理逻辑")
        print("  ✅ 功能修改3: 修改桌面程序同步按钮功能")
        print("\n🚀 系统已就绪，可以使用新的同步工作流！")
    else:
        print("⚠️  部分验证失败，请检查相关实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
