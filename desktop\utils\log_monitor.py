"""
日志监控器模块
实现对服务端日志文件的实时监控，并通过信号发送到UI组件
"""
import os
import time
from pathlib import Path
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, QObject
from PyQt6.QtWidgets import QApplication
import re
from datetime import datetime
from typing import Dict, Optional, List
import logging

class LogEntry:
    """日志条目数据类"""
    def __init__(self, timestamp: str, level: str, logger_name: str, message: str, raw_line: str = ""):
        self.timestamp = timestamp
        self.level = level
        self.logger_name = logger_name
        self.message = message
        self.raw_line = raw_line
        self.datetime_obj = self._parse_timestamp(timestamp)
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """解析时间戳字符串为datetime对象"""
        try:
            # 尝试解析标准格式: 2025-07-22 14:52:28,815
            return datetime.strptime(timestamp_str.split(',')[0], '%Y-%m-%d %H:%M:%S')
        except (ValueError, IndexError):
            return None
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp,
            'level': self.level,
            'logger_name': self.logger_name,
            'message': self.message,
            'raw_line': self.raw_line,
            'datetime': self.datetime_obj
        }

class LogParser:
    """日志解析器"""
    
    # 标准日志格式正则表达式
    LOG_PATTERN = re.compile(
        r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})\s*-\s*'
        r'(?P<logger_name>[^\s]+)\s*-\s*'
        r'(?P<level>DEBUG|INFO|WARNING|ERROR|CRITICAL)\s*-\s*'
        r'(?P<message>.*)'
    )
    
    @classmethod
    def parse_line(cls, line: str) -> Optional[LogEntry]:
        """解析单行日志"""
        line = line.strip()
        if not line:
            return None
            
        match = cls.LOG_PATTERN.match(line)
        if match:
            return LogEntry(
                timestamp=match.group('timestamp'),
                level=match.group('level'),
                logger_name=match.group('logger_name'),
                message=match.group('message'),
                raw_line=line
            )
        else:
            # 如果不匹配标准格式，作为普通消息处理
            return LogEntry(
                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S,000'),
                level='INFO',
                logger_name='system',
                message=line,
                raw_line=line
            )

class LogFileMonitor(QThread):
    """日志文件监控线程"""
    
    # 信号定义
    log_entry_received = pyqtSignal(object)  # LogEntry对象
    file_error = pyqtSignal(str)  # 文件错误信息
    monitoring_started = pyqtSignal(str)  # 监控开始信号
    monitoring_stopped = pyqtSignal()  # 监控停止信号
    
    def __init__(self, log_file_path: str, parent=None):
        super().__init__(parent)
        self.log_file_path = Path(log_file_path)
        self.is_monitoring = False
        self.file_position = 0
        self.check_interval = 0.5  # 检查间隔（秒）
        
    def start_monitoring(self):
        """开始监控"""
        self.is_monitoring = True
        if not self.isRunning():
            self.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.isRunning():
            self.quit()
            self.wait(3000)  # 等待最多3秒
    
    def run(self):
        """监控线程主循环"""
        try:
            self.monitoring_started.emit(str(self.log_file_path))
            
            # 如果文件存在，移动到文件末尾
            if self.log_file_path.exists():
                with open(self.log_file_path, 'r', encoding='utf-8') as f:
                    f.seek(0, 2)  # 移动到文件末尾
                    self.file_position = f.tell()
            
            while self.is_monitoring:
                try:
                    self._check_file_updates()
                    self.msleep(int(self.check_interval * 1000))
                except Exception as e:
                    self.file_error.emit(f"监控文件时发生错误: {e}")
                    self.msleep(5000)  # 错误时等待5秒再重试
                    
        except Exception as e:
            self.file_error.emit(f"启动监控失败: {e}")
        finally:
            self.monitoring_stopped.emit()
    
    def _check_file_updates(self):
        """检查文件更新"""
        if not self.log_file_path.exists():
            return
            
        try:
            current_size = self.log_file_path.stat().st_size
            
            # 如果文件被截断或重新创建
            if current_size < self.file_position:
                self.file_position = 0
            
            # 如果有新内容
            if current_size > self.file_position:
                with open(self.log_file_path, 'r', encoding='utf-8') as f:
                    f.seek(self.file_position)
                    new_lines = f.readlines()
                    self.file_position = f.tell()
                    
                    # 解析并发送新的日志条目
                    for line in new_lines:
                        log_entry = LogParser.parse_line(line)
                        if log_entry:
                            self.log_entry_received.emit(log_entry)
                            
        except (IOError, OSError) as e:
            self.file_error.emit(f"读取日志文件失败: {e}")

class ServerStatusMonitor(QObject):
    """服务端状态监控器"""
    
    # 信号定义
    status_changed = pyqtSignal(str)  # 状态变化信号
    log_received = pyqtSignal(object)  # 日志接收信号
    error_occurred = pyqtSignal(str)  # 错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_monitor = None
        self.server_status = "未知"
        self.log_entries = []  # 存储日志条目
        self.max_log_entries = 1000  # 最大日志条目数
        
    def start_monitoring(self, log_file_path: str):
        """开始监控服务端状态"""
        try:
            # 停止现有监控
            self.stop_monitoring()
            
            # 创建新的日志监控器
            self.log_monitor = LogFileMonitor(log_file_path, self)
            
            # 连接信号
            self.log_monitor.log_entry_received.connect(self._on_log_entry_received)
            self.log_monitor.file_error.connect(self._on_file_error)
            self.log_monitor.monitoring_started.connect(self._on_monitoring_started)
            self.log_monitor.monitoring_stopped.connect(self._on_monitoring_stopped)
            
            # 开始监控
            self.log_monitor.start_monitoring()
            
        except Exception as e:
            self.error_occurred.emit(f"启动状态监控失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.log_monitor:
            self.log_monitor.stop_monitoring()
            self.log_monitor = None
    
    def get_recent_logs(self, count: int = 100) -> List[LogEntry]:
        """获取最近的日志条目"""
        return self.log_entries[-count:] if self.log_entries else []
    
    def _on_log_entry_received(self, log_entry: LogEntry):
        """处理接收到的日志条目"""
        # 添加到日志列表
        self.log_entries.append(log_entry)
        
        # 限制日志条目数量
        if len(self.log_entries) > self.max_log_entries:
            self.log_entries = self.log_entries[-self.max_log_entries:]
        
        # 分析日志内容，更新服务端状态
        self._analyze_log_for_status(log_entry)
        
        # 发送日志信号
        self.log_received.emit(log_entry)
    
    def _analyze_log_for_status(self, log_entry: LogEntry):
        """分析日志内容，判断服务端状态"""
        message = log_entry.message.lower()
        
        # 根据日志内容判断状态
        if "启动考勤系统服务端" in message or "application startup complete" in message:
            self._update_status("运行中")
        elif "数据库初始化成功" in message:
            self._update_status("初始化中")
        elif "error" in message or "failed" in message or log_entry.level == "ERROR":
            self._update_status("错误")
        elif "shutdown" in message or "stopping" in message:
            self._update_status("停止中")
    
    def _update_status(self, new_status: str):
        """更新服务端状态"""
        if self.server_status != new_status:
            self.server_status = new_status
            self.status_changed.emit(new_status)
    
    def _on_file_error(self, error_message: str):
        """处理文件错误"""
        self.error_occurred.emit(error_message)
        self._update_status("监控错误")
    
    def _on_monitoring_started(self, file_path: str):
        """监控开始回调"""
        self._update_status("监控中")
    
    def _on_monitoring_stopped(self):
        """监控停止回调"""
        self._update_status("监控已停止")
