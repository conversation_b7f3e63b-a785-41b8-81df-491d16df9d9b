"""
PyInstaller hook for FastAPI
确保FastAPI及其依赖被正确包含
"""

from PyInstaller.utils.hooks import collect_all, collect_submodules

# 收集FastAPI的所有模块
datas, binaries, hiddenimports = collect_all('fastapi')

# 添加额外的隐藏导入
hiddenimports += [
    'fastapi.applications',
    'fastapi.routing',
    'fastapi.middleware',
    'fastapi.middleware.cors',
    'fastapi.middleware.trustedhost',
    'fastapi.responses',
    'fastapi.exceptions',
    'fastapi.security',
    'fastapi.params',
    'fastapi.encoders',
    'fastapi.utils',
    'starlette',
    'starlette.applications',
    'starlette.routing',
    'starlette.middleware',
    'starlette.responses',
    'starlette.exceptions',
    'starlette.requests',
    'starlette.background',
    'starlette.staticfiles',
    'pydantic',
    'pydantic.fields',
    'pydantic.main',
    'pydantic.types',
    'pydantic.validators',
    'pydantic.json',
]
