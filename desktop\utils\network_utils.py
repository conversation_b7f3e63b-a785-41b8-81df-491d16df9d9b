"""
网络工具模块
提供IP地址获取等网络相关功能
"""
import socket
import subprocess
import platform
import logging
from typing import Optional, List

logger = logging.getLogger(__name__)

def get_local_ip_address() -> Optional[str]:
    """
    获取本机的局域网IP地址
    
    Returns:
        str: IP地址字符串，如果获取失败返回None
    """
    try:
        # 方法1: 通过连接外部地址获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 连接到一个外部地址（不会实际发送数据）
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            logger.debug(f"通过socket连接获取IP: {ip}")
            return ip
    except Exception as e:
        logger.warning(f"方法1获取IP失败: {e}")
    
    try:
        # 方法2: 通过hostname获取IP
        hostname = socket.gethostname()
        ip = socket.gethostbyname(hostname)
        # 过滤掉回环地址
        if ip != "127.0.0.1":
            logger.debug(f"通过hostname获取IP: {ip}")
            return ip
    except Exception as e:
        logger.warning(f"方法2获取IP失败: {e}")
    
    try:
        # 方法3: 在Windows上使用ipconfig命令
        if platform.system() == "Windows":
            result = subprocess.run(
                ["ipconfig"], 
                capture_output=True, 
                text=True, 
                encoding='gbk'  # Windows中文系统使用gbk编码
            )
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if "IPv4" in line and "192.168." in line:
                        # 提取IP地址
                        ip = line.split(':')[-1].strip()
                        logger.debug(f"通过ipconfig获取IP: {ip}")
                        return ip
    except Exception as e:
        logger.warning(f"方法3获取IP失败: {e}")
    
    logger.error("所有方法都无法获取IP地址")
    return None

def get_all_local_ip_addresses() -> List[str]:
    """
    获取本机所有的IP地址
    
    Returns:
        List[str]: IP地址列表
    """
    ip_addresses = []
    
    try:
        # 获取所有网络接口的IP地址
        hostname = socket.gethostname()
        
        # 获取所有地址信息
        addr_info = socket.getaddrinfo(hostname, None)
        
        for info in addr_info:
            ip = info[4][0]
            # 过滤IPv4地址，排除回环地址
            if '.' in ip and ip != '127.0.0.1':
                if ip not in ip_addresses:
                    ip_addresses.append(ip)
                    
    except Exception as e:
        logger.warning(f"获取所有IP地址失败: {e}")
    
    return ip_addresses

def is_ip_address_valid(ip: str) -> bool:
    """
    验证IP地址格式是否正确
    
    Args:
        ip: IP地址字符串
        
    Returns:
        bool: 是否为有效的IP地址
    """
    try:
        socket.inet_aton(ip)
        return True
    except socket.error:
        return False

def get_network_interface_info() -> dict:
    """
    获取网络接口信息
    
    Returns:
        dict: 包含网络接口信息的字典
    """
    info = {
        'hostname': '',
        'primary_ip': '',
        'all_ips': [],
        'platform': platform.system()
    }
    
    try:
        info['hostname'] = socket.gethostname()
        info['primary_ip'] = get_local_ip_address()
        info['all_ips'] = get_all_local_ip_addresses()
    except Exception as e:
        logger.error(f"获取网络接口信息失败: {e}")
    
    return info

def format_ip_display(ip: Optional[str]) -> str:
    """
    格式化IP地址显示

    Args:
        ip: IP地址字符串

    Returns:
        str: 格式化后的显示字符串
    """
    if ip and is_ip_address_valid(ip):
        return f"当前IP地址: {ip}"
    else:
        return "当前IP地址: 获取失败"

def get_preferred_ip_address() -> Optional[str]:
    """
    获取优先的IP地址用于UDP广播

    优先级：
    1. 192.168.x.x网段
    2. 10.x.x.x网段
    3. 172.16.x.x-172.31.x.x网段
    排除：127.0.0.1和169.254.x.x

    Returns:
        str: 优先的IP地址，如果获取失败返回None
    """
    try:
        all_ips = get_all_local_ip_addresses()
        if not all_ips:
            logger.warning("未获取到任何IP地址")
            return None

        # 按优先级分类IP地址
        priority_1_ips = []  # 192.168.x.x
        priority_2_ips = []  # 10.x.x.x
        priority_3_ips = []  # 172.16.x.x-172.31.x.x

        for ip in all_ips:
            if not is_ip_address_valid(ip):
                continue

            # 排除回环地址和APIPA地址
            if ip.startswith('127.') or ip.startswith('169.254.'):
                continue

            # 按优先级分类
            if ip.startswith('192.168.'):
                priority_1_ips.append(ip)
            elif ip.startswith('10.'):
                priority_2_ips.append(ip)
            elif ip.startswith('172.'):
                # 检查是否在172.16.x.x-172.31.x.x范围内
                parts = ip.split('.')
                if len(parts) >= 2:
                    try:
                        second_octet = int(parts[1])
                        if 16 <= second_octet <= 31:
                            priority_3_ips.append(ip)
                    except ValueError:
                        continue

        # 按优先级返回第一个可用的IP
        if priority_1_ips:
            selected_ip = priority_1_ips[0]
            logger.debug(f"选择优先级1 IP地址: {selected_ip}")
            return selected_ip
        elif priority_2_ips:
            selected_ip = priority_2_ips[0]
            logger.debug(f"选择优先级2 IP地址: {selected_ip}")
            return selected_ip
        elif priority_3_ips:
            selected_ip = priority_3_ips[0]
            logger.debug(f"选择优先级3 IP地址: {selected_ip}")
            return selected_ip
        else:
            # 如果没有符合条件的私有IP，返回第一个可用的IP
            for ip in all_ips:
                if is_ip_address_valid(ip) and not ip.startswith('127.') and not ip.startswith('169.254.'):
                    logger.debug(f"选择备用 IP地址: {ip}")
                    return ip

            logger.warning("未找到合适的IP地址用于UDP广播")
            return None

    except Exception as e:
        logger.error(f"获取优先IP地址失败: {e}")
        return None

# 测试函数
def test_network_utils():
    """测试网络工具函数"""
    print("=" * 50)
    print("网络工具测试")
    print("=" * 50)
    
    # 测试获取主IP地址
    primary_ip = get_local_ip_address()
    print(f"主IP地址: {primary_ip}")
    
    # 测试获取所有IP地址
    all_ips = get_all_local_ip_addresses()
    print(f"所有IP地址: {all_ips}")
    
    # 测试网络接口信息
    network_info = get_network_interface_info()
    print(f"网络接口信息: {network_info}")
    
    # 测试格式化显示
    display_text = format_ip_display(primary_ip)
    print(f"显示文本: {display_text}")
    
    print("=" * 50)

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.DEBUG)
    test_network_utils()
