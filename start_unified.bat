@echo off
setlocal

echo ========================================
echo Attendance System Launcher
echo ========================================
echo.

echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ and add to PATH
    pause
    exit /b 1
)

echo Python environment OK

echo.
echo Checking virtual environment...
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo WARNING: Virtual environment not found
)

echo.
echo Checking dependencies...
python -c "import fastapi, uvicorn, PyQt6, sqlalchemy" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Missing required packages
    if exist "requirements.txt" (
        echo Installing dependencies...
        pip install -r requirements.txt
        if errorlevel 1 (
            echo Failed to install dependencies
            pause
            exit /b 1
        )
    ) else (
        echo requirements.txt not found
        pause
        exit /b 1
    )
)

echo Dependencies OK

echo.
echo Starting Attendance System...
echo Server: http://localhost:8000
echo.

python start_unified.py

echo.
echo System stopped
pause