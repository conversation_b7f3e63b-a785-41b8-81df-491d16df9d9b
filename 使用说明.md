# 考勤系统使用说明

## 快速启动

### 方法1：双击启动（推荐）
直接双击 `start_unified.bat` 文件即可启动完整的考勤系统。

### 方法2：命令行启动
```bash
# 启动完整系统（服务端 + 桌面程序）
python start_unified.py

# 仅启动服务端
python start_unified.py --server-only

# 仅启动桌面程序
python start_unified.py --desktop-only

# 自定义端口启动
python start_unified.py --port 8001

# 调试模式启动
python start_unified.py --debug
```

## 系统功能

### 1. 服务端状态监控
- **实时状态显示**：顶部区域显示服务端运行状态
- **日志监控**：实时显示服务端日志信息
- **状态指示灯**：
  - 🟢 绿色：服务端正常运行
  - 🟠 橙色：初始化中或停止中
  - 🔴 红色：错误状态
  - ⚪ 灰色：未知或监控已停止

### 2. 设备管理
- **设备列表**：显示所有连接的考勤设备
- **同步控制**：手动触发设备数据同步
- **状态监控**：实时显示设备在线状态和最后心跳时间

### 3. 考勤记录管理
- **数据显示**：表格形式显示考勤记录
- **数据导出**：支持导出为Excel格式
- **实时更新**：自动刷新最新的考勤数据

### 4. 系统日志
- **多级别日志**：支持DEBUG、INFO、WARNING、ERROR级别
- **日志过滤**：可按级别筛选显示
- **历史记录**：保存历史日志信息

## 界面布局

```
┌─────────────────────────────────────────────────────────┐
│                    服务端状态区域                          │
│  ┌─────────────┐  ┌─────────────────────────────────┐    │
│  │ 状态指示器   │  │        实时日志显示              │    │
│  │ 控制按钮     │  │        (支持过滤和滚动)          │    │
│  └─────────────┘  └─────────────────────────────────┘    │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    主要工作区域                          │
│  ┌─────────────┐  ┌─────────────────────────────────┐    │
│  │   设备管理   │  │        考勤记录显示              │    │
│  │             │  │                               │    │
│  │ • 设备列表   │  │ • 考勤数据表格                  │    │
│  │ • 同步按钮   │  │ • 导出功能                     │    │
│  │ • 导出按钮   │  │ • 系统日志                     │    │
│  └─────────────┘  └─────────────────────────────────┘    │
└─────────────────────────────────────────────────────────┘
```

## 启动流程

1. **环境检查**：自动检查Python环境和依赖包
2. **虚拟环境**：自动激活虚拟环境（如果存在）
3. **依赖安装**：自动安装缺失的依赖包
4. **服务端启动**：启动FastAPI服务端，默认端口8000
5. **桌面程序启动**：启动PyQt6桌面管理程序
6. **状态监控**：建立服务端状态监控连接

## 故障排除

### 常见问题

**1. 端口被占用**
```
错误: 端口 localhost:8000 已被占用
```
解决方案：
- 使用其他端口：`python start_unified.py --port 8001`
- 或者关闭占用端口的程序

**2. Python环境问题**
```
错误: Python not found
```
解决方案：
- 确保已安装Python 3.8或更高版本
- 将Python添加到系统PATH环境变量

**3. 依赖包缺失**
```
错误: Missing required packages
```
解决方案：
- 手动安装：`pip install -r requirements.txt`
- 或者让启动脚本自动安装

**4. 桌面程序无法启动**
```
错误: Qt platform plugin could not be initialized
```
解决方案：
- 重新安装PyQt6：`pip uninstall PyQt6 && pip install PyQt6`
- 检查系统是否支持图形界面

### 日志文件位置
- 服务端日志：`attendance_server/logs/server.log`
- 系统运行日志：控制台输出

## 系统要求

- **操作系统**：Windows 10/11
- **Python版本**：3.8或更高版本
- **内存**：至少2GB可用内存
- **磁盘空间**：至少500MB可用空间
- **网络**：局域网连接（用于设备通信）

## 技术支持

如果遇到问题，请：
1. 查看控制台输出的错误信息
2. 检查日志文件中的详细错误
3. 确认系统环境符合要求
4. 尝试重新启动系统

## 更新日志

### v1.0.0 (2025-07-22)
- ✅ 实现统一启动脚本
- ✅ 添加服务端状态实时监控
- ✅ 重构桌面程序UI布局
- ✅ 完善错误处理和日志记录
- ✅ 支持自动依赖检查和安装
