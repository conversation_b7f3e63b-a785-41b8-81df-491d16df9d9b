"""
Device management API endpoints.
"""
from fastapi import API<PERSON>outer, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

from ..db.database import get_db
from ..db.crud import get_device, get_all_devices, update_device_sync_status
from ..schemas.device import DeviceResponse

router = APIRouter()

@router.get("/devices", response_model=List[DeviceResponse])
async def list_devices(db: Session = Depends(get_db)) -> List[DeviceResponse]:
    """Get list of all devices with their current status."""
    try:
        devices = get_all_devices(db)
        return [
            DeviceResponse.model_validate({
                "device_id": device.device_id,
                "name": device.name,
                "type": device.type,
                "group": device.group,
                "notes": device.notes,
                "is_online": bool(device.is_online),
                "sync_status": device.sync_status,
                "last_heartbeat": device.last_heartbeat,
                "created_at": device.created_at,
                "updated_at": device.updated_at
            })
            for device in devices
        ]
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching devices: {str(e)}"
        )

@router.get("/devices/{device_id}", response_model=DeviceResponse)
async def get_device_status(device_id: str, db: Session = Depends(get_db)) -> DeviceResponse:
    """Get status of a specific device."""
    device = get_device(db, device_id)
    if not device:
        raise HTTPException(
            status_code=404,
            detail=f"Device {device_id} not found"
        )
    return DeviceResponse.model_validate({
        "device_id": device.device_id,
        "name": device.name,
        "type": device.type,
        "group": device.group,
        "notes": device.notes,
        "is_online": bool(device.is_online),
        "sync_status": device.sync_status,
        "last_heartbeat": device.last_heartbeat,
        "created_at": device.created_at,
        "updated_at": device.updated_at
    })

@router.post("/devices/{device_id}/sync")
async def request_sync(device_id: str, db: Session = Depends(get_db)) -> dict:
    """Request synchronization from a specific device."""
    device = get_device(db, device_id)
    if not device:
        raise HTTPException(
            status_code=404,
            detail=f"Device {device_id} not found"
        )
    
    if not bool(device.is_online):
        raise HTTPException(
            status_code=400,
            detail=f"Device {device_id} is offline"
        )
    
    update_device_sync_status(db, device_id, 'pending')
    return {
        "status": "success",
        "message": f"Sync requested for device {device_id}"
    } 