#!/usr/bin/env python3
"""
考勤系统统一程序打包脚本
将桌面程序和服务端程序打包成单一的可执行文件
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path
import argparse

class UnifiedAppBuilder:
    """统一程序构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "unified_package.spec"
        
    def log(self, message: str, level: str = "INFO"):
        """打印日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def run_command(self, cmd: list, cwd: Path = None, check: bool = True) -> subprocess.CompletedProcess:
        """运行命令并返回结果"""
        if cwd is None:
            cwd = self.project_root
            
        self.log(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                check=check,
                encoding='utf-8',
                errors='replace'
            )
            
            if result.stdout:
                # 只显示重要的输出信息
                lines = result.stdout.strip().split('\n')
                for line in lines[-3:]:  # 只显示最后3行
                    if line.strip():
                        self.log(f"输出: {line.strip()}")
            
            if result.stderr:
                self.log(f"警告: {result.stderr.strip()}", "WARNING")
                
            return result
            
        except subprocess.CalledProcessError as e:
            self.log(f"命令执行失败: {e}", "ERROR")
            if e.stdout:
                self.log(f"标准输出: {e.stdout}", "ERROR")
            if e.stderr:
                self.log(f"错误输出: {e.stderr}", "ERROR")
            raise
    
    def check_environment(self):
        """检查环境"""
        self.log("检查Python环境...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            raise RuntimeError(f"需要Python 3.8或更高版本，当前版本: {sys.version}")
        
        self.log(f"Python版本: {sys.version}")
        
        # 检查必要模块
        required_modules = ['PyQt6', 'fastapi', 'uvicorn', 'pandas', 'openpyxl', 'sqlite3', 'sqlalchemy']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                self.log(f"模块检查通过: {module}")
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            self.log(f"缺少必要模块: {', '.join(missing_modules)}", "ERROR")
            return False
        
        return True
    
    def install_dependencies(self):
        """安装打包依赖"""
        self.log("安装打包依赖...")
        
        # 安装PyInstaller
        try:
            import PyInstaller
            self.log(f"PyInstaller已安装: {PyInstaller.__version__}")
        except ImportError:
            self.log("安装PyInstaller...")
            self.run_command([sys.executable, "-m", "pip", "install", "pyinstaller>=6.0.0"])
        
        # 检查并安装其他依赖
        requirements_file = self.project_root / "requirements.txt"
        if requirements_file.exists():
            self.log("安装项目依赖...")
            self.run_command([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
        else:
            # 安装基本依赖
            basic_deps = [
                "PyQt6>=6.6.0",
                "fastapi>=0.104.0",
                "uvicorn>=0.24.0",
                "pandas>=2.0.0",
                "openpyxl>=3.1.0",
                "sqlalchemy>=2.0.0",
                "pydantic>=2.0.0",
                "starlette>=0.27.0"
            ]
            for dep in basic_deps:
                try:
                    self.run_command([sys.executable, "-m", "pip", "install", dep])
                except:
                    self.log(f"安装 {dep} 失败，继续...", "WARNING")
    
    def clean_build_directories(self):
        """清理构建目录"""
        self.log("清理构建目录...")
        
        for directory in [self.dist_dir, self.build_dir]:
            if directory.exists():
                self.log(f"删除目录: {directory}")
                shutil.rmtree(directory)
        
        # 清理Python缓存
        pycache_dirs = list(self.project_root.rglob("__pycache__"))
        for cache_dir in pycache_dirs:
            if cache_dir.is_dir():
                shutil.rmtree(cache_dir)
        
        self.log("构建目录清理完成")
    
    def prepare_directories(self):
        """准备必要的目录"""
        self.log("准备必要的目录...")
        
        directories = [
            self.project_root / "temp",
            self.project_root / "temp_bak", 
            self.project_root / "temp_cfg",
            self.project_root / "logs",
            self.project_root / "runtime_hooks",
            self.project_root / "hooks"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            # 创建空的__init__.py文件（如果需要）
            if directory.name in ['hooks']:
                init_file = directory / "__init__.py"
                if not init_file.exists():
                    init_file.touch()
        
        self.log("目录准备完成")
    
    def build_executable(self):
        """构建可执行文件"""
        self.log("开始构建统一程序可执行文件...")
        
        if not self.spec_file.exists():
            raise FileNotFoundError(f"规格文件不存在: {self.spec_file}")
        
        # 使用PyInstaller构建
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",  # 清理临时文件
            "--noconfirm",  # 不询问确认
            "--log-level", "INFO",  # 设置日志级别
            str(self.spec_file)
        ]
        
        self.log("开始PyInstaller构建过程...")
        self.run_command(cmd)
        
        # 检查构建结果
        exe_file = self.dist_dir / "AttendanceSystem.exe"
        if exe_file.exists():
            file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
            self.log(f"构建成功! 可执行文件: {exe_file}")
            self.log(f"文件大小: {file_size:.2f} MB")
            return exe_file
        else:
            raise RuntimeError("构建失败，未找到可执行文件")
    
    def test_executable(self, exe_file: Path):
        """测试可执行文件"""
        self.log("测试可执行文件...")
        
        if not exe_file.exists():
            raise FileNotFoundError(f"可执行文件不存在: {exe_file}")
        
        # 检查文件大小
        file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        if file_size > 100:  # 应该大于100MB（包含了所有依赖）
            self.log("可执行文件大小检查通过")
        else:
            self.log("可执行文件大小异常，可能缺少依赖", "WARNING")
    
    def create_package(self, exe_file: Path):
        """创建分发包"""
        self.log("创建分发包...")
        
        # 创建分发目录
        package_dir = self.dist_dir / "AttendanceSystem_Package"
        package_dir.mkdir(exist_ok=True)
        
        # 复制可执行文件
        shutil.copy2(exe_file, package_dir / "AttendanceSystem.exe")
        
        # 创建使用说明
        readme_content = """# 考勤系统统一程序

## 系统要求
- Windows 10 或更高版本
- 无需安装Python环境

## 功能特性
本程序集成了完整的考勤系统功能：
- 桌面管理界面
- HTTP API服务端（端口8000）
- UDP广播服务（端口37020）
- 心跳请求处理
- 文件上传处理
- 数据库操作
- Excel数据导出

## 使用方法
1. 双击 AttendanceSystem.exe 启动程序
2. 程序会自动启动所有服务：
   - 桌面管理界面
   - HTTP服务端（监听端口8000）
   - UDP广播服务（端口37020）
3. 程序会自动创建必要的工作目录：
   - temp/: 临时文件目录
   - temp_bak/: 数据备份目录
   - temp_cfg/: 配置文件目录
   - logs/: 日志文件目录

## 网络功能
- HTTP服务: http://localhost:8000
- UDP广播: 端口37020
- 心跳请求: /api/heartbeat/{device_id}
- 文件上传: /api/heartbeat/upload

## 注意事项
- 首次运行时会进行初始化设置
- 请确保端口8000和37020未被其他程序占用
- 建议以管理员身份运行以确保网络功能正常
- 程序会在控制台显示服务端日志信息

## 故障排除
1. 如果程序无法启动，请检查是否有杀毒软件阻止
2. 如果端口被占用，请关闭占用端口的其他程序
3. 确保Windows防火墙允许程序访问网络
4. 如遇问题，请查看控制台输出或logs目录中的日志文件

## 版本信息
- 版本: 1.0.0 (统一版本)
- 构建时间: """ + time.strftime("%Y-%m-%d %H:%M:%S") + """
- 包含功能: 桌面程序 + 服务端程序
"""
        
        readme_file = package_dir / "使用说明.txt"
        readme_file.write_text(readme_content, encoding='utf-8')
        
        self.log(f"分发包创建完成: {package_dir}")
        return package_dir
    
    def build(self, clean: bool = True, test: bool = True):
        """执行完整的构建流程"""
        try:
            self.log("开始构建考勤系统统一程序")
            
            # 1. 检查环境
            if not self.check_environment():
                raise RuntimeError("环境检查失败")
            
            # 2. 安装依赖
            self.install_dependencies()
            
            # 3. 清理构建目录
            if clean:
                self.clean_build_directories()
            
            # 4. 准备目录
            self.prepare_directories()
            
            # 5. 构建可执行文件
            exe_file = self.build_executable()
            
            # 6. 测试可执行文件
            if test:
                self.test_executable(exe_file)
            
            # 7. 创建分发包
            package_dir = self.create_package(exe_file)
            
            self.log("构建完成!")
            self.log(f"可执行文件: {exe_file}")
            self.log(f"分发包: {package_dir}")
            
            return exe_file, package_dir
            
        except Exception as e:
            self.log(f"构建失败: {e}", "ERROR")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="考勤系统统一程序打包工具")
    parser.add_argument("--no-clean", action="store_true", help="不清理构建目录")
    parser.add_argument("--no-test", action="store_true", help="不测试可执行文件")
    
    args = parser.parse_args()
    
    builder = UnifiedAppBuilder()
    
    try:
        exe_file, package_dir = builder.build(
            clean=not args.no_clean,
            test=not args.no_test
        )
        
        print("\n" + "="*70)
        print("🎉 考勤系统统一程序构建成功!")
        print(f"📁 可执行文件: {exe_file}")
        print(f"📦 分发包目录: {package_dir}")
        print("="*70)
        print("\n功能特性:")
        print("✅ 桌面管理界面")
        print("✅ HTTP API服务端（端口8000）")
        print("✅ UDP广播服务（端口37020）")
        print("✅ 心跳请求处理")
        print("✅ 文件上传处理")
        print("✅ 数据库操作")
        print("✅ Excel数据导出")
        print("\n使用方法:")
        print("1. 进入分发包目录")
        print("2. 双击 AttendanceSystem.exe 启动程序")
        print("3. 程序会自动启动所有服务")
        
    except Exception as e:
        print(f"\n❌ 构建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
