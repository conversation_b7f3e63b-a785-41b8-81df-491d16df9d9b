#!/usr/bin/env python3
"""
考勤系统服务端启动脚本
"""
import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
def get_project_root():
    """获取项目根目录"""
    if getattr(sys, 'frozen', False):
        # 打包环境：使用可执行文件所在目录
        return Path(sys.executable).parent
    else:
        # 开发环境：使用脚本文件所在目录
        return Path(__file__).parent

project_root = get_project_root()
sys.path.insert(0, str(project_root))

# 设置默认语言环境
def setup_global_language():
    """设置全局语言环境"""
    # 设置环境变量
    os.environ['LANG'] = 'zh_CN.UTF-8'
    os.environ['LC_ALL'] = 'zh_CN.UTF-8'
    os.environ['LC_MESSAGES'] = 'zh_CN.UTF-8'
    os.environ['TZ'] = 'Asia/Shanghai'
    
    # 设置系统区域
    try:
        import locale
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN')
        except locale.Error:
            pass

def main():
    # 设置全局语言环境
    setup_global_language()
    
    parser = argparse.ArgumentParser(description="考勤系统服务端")
    parser.add_argument("--host", default="0.0.0.0", help="监听地址")
    parser.add_argument("--port", type=int, default=8000, help="监听端口")
    parser.add_argument("--reload", action="store_true", help="开发模式")
    parser.add_argument("--desktop", action="store_true", help="启动桌面程序")
    
    args = parser.parse_args()
    
    if args.desktop:
        # 启动桌面程序
        from desktop.main_window import main as desktop_main
        desktop_main()
    else:
        # 启动Web服务
        try:
            # 导入attendance_server模块
            from attendance_server.main import app
            import uvicorn
            
            print(f"启动考勤系统服务端: http://{args.host}:{args.port}")
            uvicorn.run(
                app,
                host=args.host,
                port=args.port,
                reload=args.reload,
                log_level="debug"
            )
        except ImportError as e:
            print(f"错误: 无法导入Web应用模块: {e}")
            print("请确保attendance_server目录存在并包含必要的模块")
            sys.exit(1)

if __name__ == "__main__":
    main() 