# 项目开发规则 (RULES.md)

## 1. 技术选型规范

### 核心技术栈
- **开发语言**: Python 3.13.1 及以上版本
- **Web框架**: FastAPI（支持异步、类型注解、自动文档）
- **数据库**: SQLite（本地解析kaoqin.db）、可选PostgreSQL/MySQL用于服务端持久化
- **前端界面**: PyQt6 或 Electron（推荐PyQt6，便于与Python集成）
- **HTTP通信**: application/json; charset=utf-8
- **依赖管理**: pip + requirements.txt

## 2. 系统架构规范

### 架构设计
```
┌────────────┐      HTTP/JSON      ┌────────────┐
│  客户端设备 │ <----------------> │  服务端后台 │
└────────────┘                   └────────────┘
                                         │
                                         ▼
                                 ┌────────────┐
                                 │  管理员桌面 │
                                 └────────────┘
```

### 核心模块
1. **HTTP接口服务模块** - 接收心跳包、文件上传、状态查询等请求
2. **设备状态管理模块** - 维护设备在线/离线状态、最后心跳时间
3. **同步控制模块** - 响应"同步"按钮，控制requestCode逻辑
4. **数据库解析模块** - 解析上传的kaoqin.db，提取考勤数据
5. **前端UI模块** - 显示设备状态、同步按钮、解析结果
6. **异常与日志模块** - 记录操作日志、异常信息

## 3. 接口设计规范

### 3.1 心跳包接口
- **URL**: `/api/heartbeat`
- **Method**: POST
- **Content-Type**: application/json; charset=utf-8
- **请求体**:
```json
{
  "deviceId": "string",
  "recordCount": 123,
  "timestamp": 1700000000000
}
```
- **响应体**:
```json
{
  "success": true,
  "errorCode": 0,
  "requestCode": 101
}
```
- **说明**: requestCode=102时，客户端需上传数据库

### 3.2 文件上传接口
- **URL**: `/api/upload_db`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **参数**: file=kaoqin.db
- **响应体**:
```json
{
  "success": true,
  "errorCode": 0
}
```

### 3.3 获取考勤记录接口
- **URL**: `/api/attendance_records`
- **Method**: GET
- **Content-Type**: application/json; charset=utf-8
- **查询参数**:
  - deviceId（可选，string）：按设备编号过滤
  - start_time（可选，ISO8601字符串）：起始时间
  - end_time（可选，ISO8601字符串）：结束时间
- **响应体**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "deviceId": "设备编号",
      "sfz_name": "姓名",
      "sfz_id": "身份证号",
      "sfz_group": "分组",
      "enter_time": "2024-05-01T08:30:00",
      "md5sum": "md5字符串"
    }
  ],
  "errorCode": 0
}
```

## 4. 数据库设计规范

### 4.1 考勤记录表（attendance_record）
```sql
CREATE TABLE IF NOT EXISTS attendance_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id TEXT NOT NULL,
    sfz_name TEXT,
    sfz_id TEXT,
    sfz_group TEXT,
    enter_time DATETIME,
    md5sum TEXT
);
CREATE INDEX IF NOT EXISTS idx_attendance_device_time ON attendance_record(device_id, enter_time);
```

### 4.2 字段说明
- **id**: 主键，自增
- **device_id**: 设备编号，关联心跳包deviceId
- **sfz_name**: 姓名
- **sfz_id**: 身份证号
- **sfz_group**: 分组
- **enter_time**: 进场时间，建议存储为UTC时间
- **md5sum**: 唯一标识，便于去重

## 5. 同步流程规范

### 同步步骤
1. 管理员点击"同步"按钮，服务端将指定设备的requestCode设置为102
2. 客户端收到requestCode=102后，上传kaoqin.db
3. 服务端解析数据库，显示考勤数据
4. 同步状态标签实时显示设备在线/离线、同步进度

### 设备状态管理
- 维护每个deviceId的最后心跳时间、在线状态
- 超过N秒未收到心跳，自动标记为离线
- 支持多设备并发管理

## 6. 开发环境规范

### 系统要求
- **操作系统**: Windows 10/11或Linux服务器
- **Python环境**: 建议使用venv隔离
- **依赖管理**: requirements.txt
- **打包工具**: PyInstaller（支持打包为单一可执行文件）

### 部署规范
- 支持局域网/公网部署
- 前后端可打包为单一可执行文件
- 推荐Windows 10/11或Linux服务器

## 7. 异常处理与日志规范

### 错误处理
- 所有接口返回标准错误码和信息
- 关键操作、异常写入日志文件
- 前端弹窗提示异常

### 日志记录
- 记录操作日志、异常信息
- 支持日志级别配置
- 关键操作必须记录日志

## 8. UI设计规范

### 界面要求
- **设备列表**: 显示设备编号、在线状态、最后心跳时间、同步状态
- **同步按钮**: 一键触发同步
- **数据展示区**: 表格展示解析后的考勤数据
- **日志/消息区**: 显示操作日志和异常

### 屏幕适配
- **屏幕分辨率**: 1920x1080
- **操作系统**: Windows 10电脑

## 9. 扩展性规范

### 未来可扩展点
- 支持Web端管理界面
- 支持多种数据库格式
- 支持消息推送/邮件通知
- 支持权限管理、操作审计

---

**注意**: 本规则文档为项目开发的基础规范，所有开发人员必须严格遵守。如有疑问或需要修改，请及时沟通确认。 