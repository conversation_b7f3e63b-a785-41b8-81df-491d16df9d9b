"""
PyInstaller hook for Uvicorn
确保Uvicorn及其依赖被正确包含
"""

from PyInstaller.utils.hooks import collect_all, collect_submodules

# 收集Uvicorn的所有模块
datas, binaries, hiddenimports = collect_all('uvicorn')

# 添加额外的隐藏导入
hiddenimports += [
    'uvicorn.main',
    'uvicorn.config',
    'uvicorn.server',
    'uvicorn.protocols',
    'uvicorn.protocols.http',
    'uvicorn.protocols.http.h11_impl',
    'uvicorn.protocols.http.httptools_impl',
    'uvicorn.protocols.websockets',
    'uvicorn.protocols.websockets.websockets_impl',
    'uvicorn.protocols.websockets.wsproto_impl',
    'uvicorn.lifespan',
    'uvicorn.lifespan.on',
    'uvicorn.lifespan.off',
    'uvicorn.loops',
    'uvicorn.loops.auto',
    'uvicorn.loops.asyncio',
    'uvicorn.loops.uvloop',
    'uvicorn.logging',
    'uvicorn.middleware',
    'uvicorn.middleware.proxy_headers',
    'uvicorn.middleware.wsgi',
    'uvicorn.middleware.asgi2',
    'h11',
    'httptools',
    'websockets',
    'wsproto',
]
