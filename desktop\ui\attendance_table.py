from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
from PyQt6.QtCore import Qt
import sqlite3
from datetime import datetime
from pathlib import Path
from ..utils.time_utils import timestamp_to_beijing_string

class AttendanceTableWidget(QTableWidget):
    def __init__(self):
        super().__init__()
        # 性能优化：限制显示记录数量
        self.max_display_records = 1000  # 最多显示1000条记录
        self.current_page = 0
        self.records_per_page = 500  # 每页显示500条记录
        self.all_records = []  # 存储所有记录
        self.init_ui()
        
    def init_ui(self):
        """初始化表格界面"""
        # 设置列 - 新的4列结构：姓名、人员编号、组别、打卡时间
        self.setColumnCount(4)
        self.setHorizontalHeaderLabels(["姓名", "人员编号", "组别", "打卡时间"])

        # 设置列宽自动调整
        header = self.horizontalHeader()
        for i in range(4):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
            
    def load_device_records(self, device_id: str):
        """加载设备的考勤记录"""
        try:
            # 获取数据库文件路径
            from config.path_manager import get_database_path
            db_path = get_database_path(device_id)

            if not db_path.exists():
                self.setRowCount(0)
                return

            # 连接数据库
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # 查询记录，按时间倒序排列
            cursor.execute("""
                SELECT _id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM 
                FROM KAO_QIN__RECORD 
                ORDER BY ENTER_TIME DESC
            """)
            
            records = cursor.fetchall()
            self.setRowCount(len(records))
            
            # 填充表格 - 新的4列结构
            for row, record in enumerate(records):
                # 第1列：姓名（原record[1]）
                self.setItem(row, 0, QTableWidgetItem(str(record[1] or "")))
                # 第2列：人员编号（原record[2] - 身份证号）
                self.setItem(row, 1, QTableWidgetItem(str(record[2] or "")))
                # 第3列：组别（原record[3]）
                self.setItem(row, 2, QTableWidgetItem(str(record[3] or "")))
                # 第4列：打卡时间（原record[4]）- 使用北京时间
                timestamp = record[4]
                time_str = timestamp_to_beijing_string(timestamp) if timestamp else ""
                self.setItem(row, 3, QTableWidgetItem(time_str))
            
            conn.close()
            
        except sqlite3.Error as e:
            print(f"数据库错误: {str(e)}")
            self.setRowCount(0)
        except Exception as e:
            print(f"加载记录出错: {str(e)}")
            self.setRowCount(0)

    def load_all_records(self):
        """加载所有考勤记录（从temp目录的所有文件）"""
        try:
            # 获取temp目录路径
            from config.path_manager import get_temp_dir
            temp_dir = get_temp_dir()
            if not temp_dir.exists():
                self.setRowCount(0)
                return

            # 获取所有数据库文件
            all_files = list(temp_dir.glob("*.db"))
            if not all_files:
                self.setRowCount(0)
                return

            all_records = []

            # 从每个文件中读取记录
            for db_file in all_files:
                try:
                    conn = sqlite3.connect(str(db_file))
                    cursor = conn.cursor()

                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='KAO_QIN__RECORD'")
                    if not cursor.fetchone():
                        conn.close()
                        continue

                    # 查询记录
                    cursor.execute("""
                        SELECT _id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM
                        FROM KAO_QIN__RECORD
                        ORDER BY ENTER_TIME DESC
                    """)

                    records = cursor.fetchall()
                    all_records.extend(records)
                    conn.close()

                except Exception as e:
                    print(f"读取文件 {db_file} 失败: {e}")
                    continue

            # 按时间倒序排序
            all_records.sort(key=lambda x: x[4] if x[4] else 0, reverse=True)

            # 限制显示最近100条记录
            all_records = all_records[:100]

            # 设置表格
            self.setRowCount(len(all_records))

            # 填充表格 - 新的4列结构
            for row, record in enumerate(all_records):
                # 第1列：姓名（原record[1]）
                self.setItem(row, 0, QTableWidgetItem(str(record[1] or "")))
                # 第2列：人员编号（原record[2] - 身份证号）
                self.setItem(row, 1, QTableWidgetItem(str(record[2] or "")))
                # 第3列：组别（原record[3]）
                self.setItem(row, 2, QTableWidgetItem(str(record[3] or "")))
                # 第4列：打卡时间（原record[4]）- 使用北京时间
                timestamp = record[4]
                time_str = timestamp_to_beijing_string(timestamp) if timestamp else ""
                self.setItem(row, 3, QTableWidgetItem(time_str))

        except Exception as e:
            print(f"加载所有记录失败: {e}")
            self.setRowCount(0)

    def clear_all_records(self):
        """清空所有记录"""
        self.setRowCount(0)

    def add_record_from_data(self, record_data, source_file=None):
        """从数据库记录添加一行到表格

        Args:
            record_data: 数据库记录元组 (_id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM)
            source_file: 源文件名（可选）
        """
        try:
            # 获取当前行数
            current_row_count = self.rowCount()

            # 插入新行
            self.insertRow(current_row_count)

            # 填充数据 - 4列结构：姓名、人员编号、组别、打卡时间
            # record_data结构：(_id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM)

            # 第1列：姓名（record_data[1]）
            self.setItem(current_row_count, 0, QTableWidgetItem(str(record_data[1] or "")))

            # 第2列：人员编号（record_data[2] - 身份证号）
            self.setItem(current_row_count, 1, QTableWidgetItem(str(record_data[2] or "")))

            # 第3列：组别（record_data[3]）
            self.setItem(current_row_count, 2, QTableWidgetItem(str(record_data[3] or "")))

            # 第4列：打卡时间（record_data[4]）- 使用北京时间
            timestamp = record_data[4]
            time_str = timestamp_to_beijing_string(timestamp) if timestamp else ""
            self.setItem(current_row_count, 3, QTableWidgetItem(time_str))

        except Exception as e:
            print(f"添加记录失败: {e}")

    def refresh_display(self):
        """刷新表格显示"""
        try:
            # 设置列宽自动调整
            header = self.horizontalHeader()
            for i in range(4):
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

            # 刷新表格
            self.update()

        except Exception as e:
            print(f"刷新显示失败: {e}")

    def load_temp_bak_records(self):
        """加载temp_bak目录中的所有考勤记录"""
        try:
            # 获取temp_bak目录路径
            from config.path_manager import get_temp_bak_dir
            temp_bak_dir = get_temp_bak_dir()

            if not temp_bak_dir.exists():
                self.setRowCount(0)
                return

            # 获取所有数据库文件
            all_files = list(temp_bak_dir.glob("*.db"))

            if not all_files:
                self.setRowCount(0)
                return

            all_records = []
            processed_files = 0
            total_records_from_all_files = 0

            # 从每个文件中读取记录
            for db_file in all_files:
                try:
                    conn = sqlite3.connect(str(db_file))
                    cursor = conn.cursor()

                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='KAO_QIN__RECORD'")
                    if not cursor.fetchone():
                        conn.close()
                        continue

                    # 查询记录
                    cursor.execute("""
                        SELECT _id, SFZ_NAME, SFZ_ID, SFZ_GROUP, ENTER_TIME, MD5SUM
                        FROM KAO_QIN__RECORD
                        ORDER BY ENTER_TIME DESC
                    """)

                    records = cursor.fetchall()
                    file_record_count = len(records)
                    all_records.extend(records)
                    total_records_from_all_files += file_record_count
                    processed_files += 1

                    conn.close()

                except Exception as e:
                    print(f"读取文件 {db_file} 失败: {e}")
                    continue

            # 按时间倒序排序
            all_records.sort(key=lambda x: x[4] if x[4] else 0, reverse=True)

            # 存储所有记录
            self.all_records = all_records

            # 限制显示最新100条记录（根据新需求）
            max_display_for_sync = 100
            display_records = all_records[:max_display_for_sync]

            # 如果记录数量超过限制，显示警告
            if len(all_records) > max_display_for_sync:
                print(f"信息：共有 {len(all_records)} 条记录，显示最新的 {max_display_for_sync} 条")

            # 设置表格
            self.setRowCount(len(display_records))

            # 填充表格 - 4列结构
            for row, record in enumerate(display_records):
                # 第1列：姓名（record[1]）
                self.setItem(row, 0, QTableWidgetItem(str(record[1] or "")))
                # 第2列：人员编号（record[2] - 身份证号）
                self.setItem(row, 1, QTableWidgetItem(str(record[2] or "")))
                # 第3列：组别（record[3]）
                self.setItem(row, 2, QTableWidgetItem(str(record[3] or "")))
                # 第4列：打卡时间（record[4]）- 使用北京时间
                timestamp = record[4]
                time_str = timestamp_to_beijing_string(timestamp) if timestamp else ""
                self.setItem(row, 3, QTableWidgetItem(time_str))

        except Exception as e:
            print(f"加载temp_bak记录失败: {e}")
            import traceback
            traceback.print_exc()
            self.setRowCount(0)

    def get_records_info(self):
        """获取记录统计信息"""
        total_records = len(self.all_records)
        displayed_records = self.rowCount()
        return {
            'total': total_records,
            'displayed': displayed_records,
            'max_display': self.max_display_records,
            'is_limited': total_records > self.max_display_records
        }