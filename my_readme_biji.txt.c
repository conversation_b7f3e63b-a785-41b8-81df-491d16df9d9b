

{
	
	
	
http://localhost:8000/api/heartbeat/A002
{
"device_id": "A002",
"timestamp": "2024-07-30T10:20:30Z",
"status": "online"
}

{
"device_id": "A002",
"status": "success",
"message": "Heartbeat received",
"sync_status": "none"
}



http://localhost:8000/api/upload

form-data 格式 
file  就是文件路径

device_id  A002

{
    "message": "ok",
    "device_id": "A002",
    "filename": "A002.db",
    "file_size": 20480,
    "save_path": "F:\\renzheng\\src\\kao_qin_server_v1\\dist\\temp\\A002.db"
}




{
	


//	功能更修改:
//增加 全局变量 客户端总数 , 	客户端总数 的值来自 temp_cfg 目录的   .dev 结尾的 文件的个数  如果不存在该文件 ,那么 客户端总数的  默认值 就是 1  ,序启动过程中打印 客户端总数
//增加 全局变量 客户端编码 ,客户端编码  的值来自 temp_cfg 目录的   .dev 结尾的 文件的名.有多少个 .dev 结尾的 文件 ,就有多少个 客户端编码 ,程序启动过程中打印所有的 客户端编码

请对考勤系统进行以下三个功能修改：

**功能修改1：添加全局变量 - 客户端总数**
- 在服务端和客户端代码中添加全局变量 `客户端总数`
- 该变量的值从 `temp_cfg/dev_sum.txt` 文件中读取
- 文件内容格式：纯字符串数字（例如："2" 表示2个设备，"3" 表示3个设备）
- 如果 `dev_sum.txt` 文件不存在，则创建该文件并写入默认值 "1"
- 确保在程序启动时读取该文件并初始化全局变量

**功能修改2：修改服务端心跳接口处理逻辑**
当前逻辑：检查 `temp_cfg/update.txt` 存在时，设置响应 message 为 "upload" 并删除文件

新逻辑：
1. 检查 `temp_cfg/update.txt` 文件是否存在
2. 如果文件存在：
   - 从心跳请求中提取 `device_id` 字段值，保存为临时变量 `device_id_temp`
   - 扫描 `temp` 目录下所有文件名
   - 如果不存在以 `device_id_temp` 开头的文件名，则将心跳响应的 `message` 字段设置为 "upload"
   - 统计 `temp` 目录下的文件总数
   - 如果文件总数 >= 全局变量 `客户端总数`，则删除 `temp_cfg/update.txt` 文件
3. 如果文件不存在：保持原有响应逻辑不变

**功能修改3：修改桌面程序同步按钮功能**
替换现有同步按钮逻辑为以下流程：

点击同步按钮时：
1. 删除 `temp` 目录下的所有文件
2. 将按钮文字改为 "同步中"
3. 禁用按钮（防止重复点击）
4. 创建 `temp_cfg/update.txt` 文件

当检测到 `temp_cfg/update.txt` 文件被服务端删除时：
1. 恢复按钮可点击状态
2. 将按钮文字改回 "同步"
3. 将 `temp` 目录下的所有文件移动到 `temp_bak` 目录
4. 移动过程中如果 `temp_bak` 目录存在同名文件则覆盖
5. 将 `temp_bak` 目录中所有 `*.db` 文件的内容显示到考勤记录界面中,只显示最新的100条考勤记录.

**通用要求**：
- 使用相对路径，避免硬编码绝对路径
- 确保修改不影响现有其他功能
- 需要实现文件监控机制来检测 `temp_cfg/update.txt` 文件的删除状态
	
	}
	
}
}