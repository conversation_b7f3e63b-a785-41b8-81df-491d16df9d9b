from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QDateEdit, QTimeEdit,
    QPushButton, QGroupBox, QFormLayout, QLineEdit
)
from PyQt6.QtCore import Qt, QDate, QTime
from PyQt6.QtGui import QFont
from config.app_config import get_font_family


class MakeupDialog(QDialog):
    """补卡对话框"""
    
    def __init__(self, selected_record_data, parent=None):
        super().__init__(parent)
        self.selected_record_data = selected_record_data
        self.setWindowTitle("补卡")
        self.resize(400, 350)
        
        # 设置字体
        self.font = QFont(get_font_family(), 10)
        self.setFont(self.font)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout(self)
        
        # 人员信息显示组
        info_group = QGroupBox("人员信息")
        info_layout = QFormLayout(info_group)
        
        # 姓名（只读）
        self.name_input = QLineEdit()
        self.name_input.setText(self.selected_record_data.get('name', ''))
        self.name_input.setReadOnly(True)
        self.name_input.setStyleSheet("background-color: #f0f0f0;")
        info_layout.addRow("姓名:", self.name_input)
        
        # 人员编号（只读）
        self.employee_id_input = QLineEdit()
        self.employee_id_input.setText(self.selected_record_data.get('employee_id', ''))
        self.employee_id_input.setReadOnly(True)
        self.employee_id_input.setStyleSheet("background-color: #f0f0f0;")
        info_layout.addRow("人员编号:", self.employee_id_input)
        
        # 组别（只读）
        self.group_input = QLineEdit()
        self.group_input.setText(self.selected_record_data.get('group', ''))
        self.group_input.setReadOnly(True)
        self.group_input.setStyleSheet("background-color: #f0f0f0;")
        info_layout.addRow("组别:", self.group_input)
        
        main_layout.addWidget(info_group)
        
        # 补卡时间选择组
        time_group = QGroupBox("补卡时间")
        time_layout = QFormLayout(time_group)
        
        # 日期选择器
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())  # 默认今天
        self.date_edit.setDisplayFormat("yyyy-MM-dd")
        time_layout.addRow("补卡日期:", self.date_edit)
        
        # 时间选择器
        self.time_edit = QTimeEdit()
        self.time_edit.setTime(QTime.currentTime())  # 默认当前时间
        self.time_edit.setDisplayFormat("HH:mm:ss")
        time_layout.addRow("补卡时间:", self.time_edit)
        
        main_layout.addWidget(time_group)
        
        # 说明信息
        info_label = QLabel('注意：补卡记录将保存到专用的"补卡.db"文件中')
        info_label.setFont(QFont(get_font_family(), 9))
        info_label.setStyleSheet("color: gray;")
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        self.confirm_button = QPushButton("确认补卡")
        self.confirm_button.clicked.connect(self.accept)
        self.confirm_button.setDefault(True)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.confirm_button)
        
        main_layout.addLayout(button_layout)
    
    def get_makeup_data(self):
        """获取补卡数据"""
        return {
            "name": self.name_input.text(),
            "employee_id": self.employee_id_input.text(),
            "group": self.group_input.text(),
            "date": self.date_edit.date().toString("yyyy-MM-dd"),
            "time": self.time_edit.time().toString("HH:mm:ss"),
            "datetime": f"{self.date_edit.date().toString('yyyy-MM-dd')} {self.time_edit.time().toString('HH:mm:ss')}"
        }
