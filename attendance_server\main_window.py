from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel
from PyQt6.QtCore import QTimer
from datetime import datetime, timedelta
from attendance_server.db.crud import get_db_connection

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("考勤服务器")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("服务器运行中...")
        layout.addWidget(self.status_label)
        
        # 设备列表
        self.device_table = QTableWidget()
        self.device_table.setColumnCount(4)
        self.device_table.setHorizontalHeaderLabels(["设备ID", "状态", "最后心跳", "同步状态"])
        layout.addWidget(self.device_table)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_devices)
        layout.addWidget(refresh_btn)
        
        # 设置定时器自动刷新
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_devices)
        self.timer.start(5000)  # 每5秒刷新一次
        
        # 初始加载数据
        self.refresh_devices()
    
    def refresh_devices(self):
        """刷新设备列表"""
        conn = get_db_connection()
        cur = conn.cursor()
        try:
            cur.execute('SELECT device_id, status, last_heartbeat, sync_status FROM devices')
            devices = cur.fetchall()
            
            self.device_table.setRowCount(len(devices))
            for row, device in enumerate(devices):
                # 检查心跳超时
                last_heartbeat = datetime.strptime(device[2], "%Y-%m-%d %H:%M:%S")
                is_timeout = datetime.now() - last_heartbeat > timedelta(seconds=30)
                status = "离线" if is_timeout else device[1]
                
                self.device_table.setItem(row, 0, QTableWidgetItem(device[0]))
                self.device_table.setItem(row, 1, QTableWidgetItem(status))
                self.device_table.setItem(row, 2, QTableWidgetItem(device[2]))
                self.device_table.setItem(row, 3, QTableWidgetItem(device[3]))
                
        finally:
            conn.close() 