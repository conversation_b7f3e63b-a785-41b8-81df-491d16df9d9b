"""
Database models for the attendance server application.
"""
from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean, ForeignKey, create_engine, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class Device(Base):
    """Device model for storing device information and status."""
    __tablename__ = 'devices'

    id = Column(Integer, primary_key=True)
    device_id = Column(String(50), unique=True, nullable=False)
    name = Column(String(100))
    type = Column(String(50))
    group = Column(String(50))
    notes = Column(String(200))
    is_online = Column(Boolean, default=False)
    last_heartbeat = Column(DateTime)
    sync_status = Column(String(20), default='none')  # none/pending/requested/completed/failed
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    attendance_records = relationship("AttendanceRecord", back_populates="device")

class AttendanceRecord(Base):
    """Attendance record model for storing check-in/out data."""
    __tablename__ = 'attendance_records'

    id = Column(Integer, primary_key=True)
    device_id = Column(String(50), ForeignKey('devices.device_id'), nullable=False)
    sfz_id = Column(String(50), nullable=False)
    name = Column(String(100))
    enter_time = Column(DateTime, nullable=False)
    md5sum = Column(String(32), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    device = relationship("Device", back_populates="attendance_records")

    # Add a unique constraint to prevent duplicate records
    __table_args__ = (
        UniqueConstraint('device_id', 'sfz_id', 'enter_time', 'md5sum', name='uix_attendance_record'),
    )

class SyncLog(Base):
    """Sync log model for tracking file uploads and data synchronization."""
    __tablename__ = 'sync_logs'

    id = Column(Integer, primary_key=True)
    device_id = Column(String(50), ForeignKey('devices.device_id'), nullable=False)
    file_name = Column(String(100))
    file_size = Column(Integer)
    status = Column(String(20))  # started/completed/failed
    records_count = Column(Integer, default=0)
    error_message = Column(String(500))
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)

def init_db(db_path):
    """Initialize the database and create all tables."""
    engine = create_engine(f'sqlite:///{db_path}')
    Base.metadata.create_all(engine)
    return engine 