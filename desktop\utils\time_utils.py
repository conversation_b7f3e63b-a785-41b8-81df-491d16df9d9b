"""
时间工具模块
提供统一的时区处理功能，确保所有时间显示都使用北京时间（UTC+8）
"""

from datetime import datetime, timezone, timedelta
from typing import Optional, Union

# 北京时区常量
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_timezone():
    """
    获取北京时区对象
    
    Returns:
        timezone: UTC+8时区对象
    """
    return BEIJING_TZ

def now_beijing() -> datetime:
    """
    获取当前北京时间
    
    Returns:
        datetime: 当前北京时间
    """
    return datetime.now(tz=BEIJING_TZ)

def timestamp_to_beijing_datetime(timestamp: Union[int, float], is_milliseconds: bool = True) -> datetime:
    """
    将时间戳转换为北京时间的datetime对象
    
    Args:
        timestamp: 时间戳
        is_milliseconds: 是否为毫秒时间戳，默认True
        
    Returns:
        datetime: 北京时间的datetime对象
    """
    if is_milliseconds:
        timestamp = timestamp / 1000
    
    return datetime.fromtimestamp(timestamp, tz=BEIJING_TZ)

def timestamp_to_beijing_string(timestamp: Union[int, float], 
                               format_str: str = "%Y-%m-%d %H:%M:%S",
                               is_milliseconds: bool = True) -> str:
    """
    将时间戳转换为北京时间的字符串
    
    Args:
        timestamp: 时间戳
        format_str: 时间格式字符串
        is_milliseconds: 是否为毫秒时间戳，默认True
        
    Returns:
        str: 格式化的北京时间字符串
    """
    if not timestamp:
        return ""
    
    try:
        dt = timestamp_to_beijing_datetime(timestamp, is_milliseconds)
        return dt.strftime(format_str)
    except (ValueError, OSError, OverflowError):
        return str(timestamp)

def beijing_datetime_to_string(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    将datetime对象转换为北京时间字符串
    
    Args:
        dt: datetime对象
        format_str: 时间格式字符串
        
    Returns:
        str: 格式化的时间字符串
    """
    if dt.tzinfo is None:
        # 如果没有时区信息，假设为UTC时间，转换为北京时间
        dt = dt.replace(tzinfo=timezone.utc).astimezone(BEIJING_TZ)
    elif dt.tzinfo != BEIJING_TZ:
        # 如果有时区信息但不是北京时间，转换为北京时间
        dt = dt.astimezone(BEIJING_TZ)
    
    return dt.strftime(format_str)

def get_current_date_string(format_str: str = "%Y%m%d") -> str:
    """
    获取当前北京时间的日期字符串
    
    Args:
        format_str: 日期格式字符串
        
    Returns:
        str: 格式化的日期字符串
    """
    return now_beijing().strftime(format_str)

def get_current_datetime_string(format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    获取当前北京时间的日期时间字符串
    
    Args:
        format_str: 日期时间格式字符串
        
    Returns:
        str: 格式化的日期时间字符串
    """
    return now_beijing().strftime(format_str)

def get_log_timestamp_string() -> str:
    """
    获取用于日志的时间戳字符串（包含毫秒）
    
    Returns:
        str: 格式化的日志时间戳字符串
    """
    return now_beijing().strftime("%Y-%m-%d %H:%M:%S,000")

def parse_timestamp_for_export(timestamp: Union[int, float, None]) -> dict:
    """
    解析时间戳用于Excel导出，返回多种格式
    
    Args:
        timestamp: 时间戳（毫秒）
        
    Returns:
        dict: 包含datetime、date、month等格式的字典
    """
    if not timestamp:
        return {"datetime": "", "date": "", "month": ""}
    
    try:
        dt = timestamp_to_beijing_datetime(timestamp, is_milliseconds=True)
        return {
            "datetime": dt.strftime("%Y-%m-%d %H:%M:%S"),
            "date": dt.strftime("%Y-%m-%d"),
            "month": dt.strftime("%Y-%m")
        }
    except (ValueError, OSError, OverflowError):
        return {"datetime": str(timestamp), "date": "", "month": ""}

def create_log_entry_timestamp() -> str:
    """
    创建日志条目的时间戳字符串
    专用于服务端监控错误日志
    
    Returns:
        str: 日志时间戳字符串
    """
    return get_log_timestamp_string()

# 向后兼容的函数别名
def get_beijing_time_string(format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    获取北京时间字符串（向后兼容）
    
    Args:
        format_str: 时间格式字符串
        
    Returns:
        str: 格式化的北京时间字符串
    """
    return get_current_datetime_string(format_str)

# 常用格式常量
FORMAT_DATETIME = "%Y-%m-%d %H:%M:%S"
FORMAT_DATE = "%Y-%m-%d"
FORMAT_TIME = "%H:%M:%S"
FORMAT_COMPACT_DATE = "%Y%m%d"
FORMAT_MONTH = "%Y-%m"
FORMAT_LOG_TIMESTAMP = "%Y-%m-%d %H:%M:%S,000"

# 使用示例和测试函数
def test_time_utils():
    """
    测试时间工具函数
    """
    print("=== 时间工具模块测试 ===")
    
    # 测试当前时间
    print(f"当前北京时间: {now_beijing()}")
    print(f"当前时间字符串: {get_current_datetime_string()}")
    print(f"当前日期字符串: {get_current_date_string()}")
    print(f"日志时间戳: {get_log_timestamp_string()}")
    
    # 测试时间戳转换
    test_timestamp = 1721822400000  # 2024-07-24 18:00:00 UTC
    print(f"\n测试时间戳: {test_timestamp}")
    print(f"转换为北京时间: {timestamp_to_beijing_string(test_timestamp)}")
    
    # 测试导出格式
    export_data = parse_timestamp_for_export(test_timestamp)
    print(f"导出格式: {export_data}")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_time_utils()
