"""
Pydantic models for device-related data validation.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict

class DeviceBase(BaseModel):
    """Base device model."""
    device_id: str
    name: Optional[str] = None
    type: Optional[str] = None
    group: Optional[str] = None
    notes: Optional[str] = None

class DeviceCreate(DeviceBase):
    """Device creation model."""
    pass

class DeviceUpdate(DeviceBase):
    """Device update model."""
    name: Optional[str] = None
    type: Optional[str] = None
    group: Optional[str] = None
    notes: Optional[str] = None
    is_online: Optional[bool] = None
    sync_status: Optional[str] = None

class DeviceResponse(BaseModel):
    """Device response model."""
    device_id: str
    status: str = "success"
    message: str = "File uploaded successfully"
    sync_status: str = "received"

    model_config = ConfigDict(
        from_attributes=True
    ) 