import asyncio
import aiohttp
from datetime import datetime
from typing import List, Dict, Optional
from queue import Queue, Empty
from threading import Thread, Event
from PyQt6.QtCore import QObject, pyqtSignal

class SyncController(QObject):
    sync_started = pyqtSignal(str)  # 同步开始信号
    sync_completed = pyqtSignal(str)  # 同步完成信号
    sync_error = pyqtSignal(str, str)  # 同步错误信号 (device_id, error_message)
    
    def __init__(self):
        super().__init__()
        self.base_url = "http://localhost:8000"  # API服务器地址
        self.sync_queue = Queue()  # 同步任务队列
        self.stop_event = Event()  # 停止事件
        self.sync_thread = None
        
    def start_sync(self, device_id: str):
        """开始同步设备数据"""
        # 将同步任务添加到队列
        self.sync_queue.put(device_id)
        
        # 如果同步线程未启动，则启动它
        if not self.sync_thread or not self.sync_thread.is_alive():
            self.stop_event.clear()
            self.sync_thread = Thread(target=self._sync_worker)
            self.sync_thread.daemon = True
            self.sync_thread.start()
            
    def stop_sync(self):
        """停止同步"""
        self.stop_event.set()
        if self.sync_thread and self.sync_thread.is_alive():
            self.sync_thread.join()
            
    def _sync_worker(self):
        """同步工作线程"""
        while not self.stop_event.is_set():
            try:
                # 从队列获取设备ID（等待1秒）
                device_id = self.sync_queue.get(timeout=1)
                
                # 检查停止信号
                if device_id is None:
                    break
                    
                # 发出同步开始信号
                self.sync_started.emit(device_id)
                
                # 创建事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # 执行同步
                    loop.run_until_complete(self._sync_device_data(device_id))
                finally:
                    loop.close()
                    
                # 标记任务完成
                self.sync_queue.task_done()
                
            except Empty:
                # 队列超时，继续循环
                continue
            except Exception as e:
                # 发出错误信号
                if device_id:
                    self.sync_error.emit(device_id, str(e))
                print(f"同步出错: {e}")
                continue
                
    async def _sync_device_data(self, device_id: str):
        """同步设备数据
        
        Args:
            device_id: 设备ID
        """
        async with aiohttp.ClientSession() as session:
            try:
                # 获取最后同步时间
                last_sync = await self._get_last_sync_time(session, device_id)
                
                # 获取新数据
                new_records = await self._get_new_records(session, device_id, last_sync)
                
                if new_records:
                    # 上传新数据
                    success = await self._upload_records(session, device_id, new_records)
                    if success:
                        # 更新同步时间
                        await self._update_sync_time(session, device_id)
                        
            except Exception as e:
                print(f"同步设备 {device_id} 数据失败: {e}")
                
    async def _get_last_sync_time(self, session: aiohttp.ClientSession, device_id: str) -> Optional[str]:
        """获取最后同步时间"""
        try:
            async with session.get(f"{self.base_url}/api/devices/{device_id}/last_sync") as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("last_sync")
                return None
        except Exception as e:
            print(f"获取最后同步时间失败: {e}")
            return None
            
    async def _get_new_records(self, session: aiohttp.ClientSession, device_id: str, last_sync: Optional[str]) -> List[Dict]:
        """获取新记录"""
        try:
            params = {"last_sync": last_sync} if last_sync else {}
            async with session.get(
                f"{self.base_url}/api/devices/{device_id}/records",
                params=params
            ) as response:
                if response.status == 200:
                    return await response.json()
                return []
        except Exception as e:
            print(f"获取新记录失败: {e}")
            return []
            
    async def _upload_records(self, session: aiohttp.ClientSession, device_id: str, records: List[Dict]) -> bool:
        """上传记录"""
        try:
            async with session.post(
                f"{self.base_url}/api/devices/{device_id}/records",
                json={"records": records}
            ) as response:
                return response.status == 200
        except Exception as e:
            print(f"上传记录失败: {e}")
            return False
            
    async def _update_sync_time(self, session: aiohttp.ClientSession, device_id: str) -> bool:
        """更新同步时间"""
        try:
            async with session.put(
                f"{self.base_url}/api/devices/{device_id}/last_sync",
                json={"last_sync": datetime.now().isoformat()}
            ) as response:
                return response.status == 200
        except Exception as e:
            print(f"更新同步时间失败: {e}")
            return False 