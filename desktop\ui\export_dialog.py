from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, 
    QDateEdit, QCheckBox, QPushButton, QGroupBox, QFormLayout
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from config.app_config import get_font_family

class ExportDialog(QDialog):
    """导出设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("导出设置")
        self.resize(400, 300)
        
        # 设置字体
        self.font = QFont(get_font_family(), 10)
        self.setFont(self.font)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout(self)
        
        # 文件格式选择
        format_group = QGroupBox("导出格式")
        format_layout = QFormLayout(format_group)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["Excel文件 (*.xlsx)", "CSV文件 (*.csv)"])
        format_layout.addRow("文件格式:", self.format_combo)
        
        main_layout.addWidget(format_group)
        
        # 时间范围选择
        time_group = QGroupBox("时间范围")
        time_layout = QFormLayout(time_group)

        # 时间筛选选项
        self.time_filter_combo = QComboBox()
        self.time_filter_combo.addItems([
            "全部记录",
            "最近7天",
            "最近30天",
            "最近90天",
            "自定义时间段"
        ])
        self.time_filter_combo.currentTextChanged.connect(self.on_time_filter_option_changed)
        time_layout.addRow("时间范围:", self.time_filter_combo)

        # 自定义时间段控件
        self.custom_time_widget = QGroupBox("自定义时间段")
        custom_layout = QFormLayout(self.custom_time_widget)

        # 开始日期
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-30))  # 默认30天前
        self.start_date.setDisplayFormat("yyyy-MM-dd")
        custom_layout.addRow("开始日期:", self.start_date)

        # 结束日期
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())  # 默认今天
        self.end_date.setDisplayFormat("yyyy-MM-dd")
        custom_layout.addRow("结束日期:", self.end_date)

        # 时间格式说明
        time_note = QLabel("注意：时间筛选基于考勤记录的打卡时间")
        time_note.setStyleSheet("color: gray; font-size: 9px;")
        time_note.setWordWrap(True)
        custom_layout.addRow(time_note)

        time_layout.addRow(self.custom_time_widget)

        # 默认隐藏自定义时间段控件
        self.custom_time_widget.setVisible(False)
        
        main_layout.addWidget(time_group)
        
        # 数据选项
        data_group = QGroupBox("数据选项")
        data_layout = QFormLayout(data_group)
        
        self.include_md5 = QCheckBox("包含MD5校验值")
        data_layout.addRow(self.include_md5)
        
        self.include_filename = QCheckBox("包含源文件名")
        self.include_filename.setChecked(True)  # 默认选中
        data_layout.addRow(self.include_filename)
        
        main_layout.addWidget(data_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        
        self.export_button = QPushButton("导出")
        self.export_button.clicked.connect(self.accept)
        self.export_button.setDefault(True)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.export_button)
        
        main_layout.addLayout(button_layout)
    
    def on_time_filter_option_changed(self, option):
        """时间筛选选项变化处理"""
        from PyQt6.QtCore import QDate

        if option == "自定义时间段":
            self.custom_time_widget.setVisible(True)
        else:
            self.custom_time_widget.setVisible(False)

            # 根据选项自动设置日期范围
            current_date = QDate.currentDate()
            if option == "最近7天":
                self.start_date.setDate(current_date.addDays(-7))
                self.end_date.setDate(current_date)
            elif option == "最近30天":
                self.start_date.setDate(current_date.addDays(-30))
                self.end_date.setDate(current_date)
            elif option == "最近90天":
                self.start_date.setDate(current_date.addDays(-90))
                self.end_date.setDate(current_date)
    
    def get_export_settings(self):
        """获取导出设置"""
        format_str = self.format_combo.currentText()
        format_ext = ".xlsx" if "Excel" in format_str else ".csv"

        # 判断是否使用时间筛选
        time_option = self.time_filter_combo.currentText()
        use_time_filter = time_option != "全部记录"

        return {
            "format": format_ext,
            "time_option": time_option,
            "use_time_filter": use_time_filter,
            "start_date": self.start_date.date().toString("yyyy-MM-dd") if use_time_filter else None,
            "end_date": self.end_date.date().toString("yyyy-MM-dd") if use_time_filter else None,
            "include_md5": self.include_md5.isChecked(),
            "include_filename": self.include_filename.isChecked()
        }