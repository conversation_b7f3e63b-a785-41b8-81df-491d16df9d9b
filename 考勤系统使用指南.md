# 考勤系统 Windows 10 可执行文件使用指南

## 🎯 快速开始

### 1. 系统要求
- ✅ Windows 10 或更高版本
- ✅ 无需安装Python环境
- ✅ 建议4GB以上内存
- ✅ 至少200MB可用磁盘空间

### 2. 文件说明
```
📦 AttendanceSystem_Package/
├── 📄 AttendanceSystem.exe    # 主程序（双击启动）
└── 📖 使用说明.txt           # 基本使用说明
```

### 3. 启动程序
**最简单的方式**：双击 `AttendanceSystem.exe` 文件

程序启动后会显示：
- 🖥️ 桌面程序窗口（考勤记录管理界面）
- 📊 控制台窗口（显示系统日志）

## 🔧 高级启动选项

### 自定义端口启动
```bash
# 使用端口8080
AttendanceSystem.exe --port 8080

# 使用端口9000
AttendanceSystem.exe --port 9000
```

### 单独启动组件
```bash
# 仅启动服务端（无桌面界面）
AttendanceSystem.exe --server-only --port 8000

# 仅启动桌面程序
AttendanceSystem.exe --desktop-only
```

### 调试模式
```bash
# 启用详细日志输出
AttendanceSystem.exe --debug
```

## 🌐 网络功能验证

### 1. 验证HTTP服务
启动程序后，在浏览器中访问：
```
http://localhost:8000/
```

**正常响应**：
```json
{
  "name": "考勤系统服务端",
  "version": "1.0.0",
  "status": "running"
}
```

### 2. 网络端口说明
- **HTTP服务**: 端口8000（可自定义）
- **UDP广播**: 端口37020（固定）

## 📋 主要功能

### 1. 考勤记录管理
- 📊 实时显示考勤记录
- 🕐 所有时间显示为北京时间（UTC+8）
- 🔄 自动数据同步和刷新
- 📁 数据备份和恢复

### 2. 数据导出
- 📈 Excel格式导出
- 📅 按日期范围筛选
- 👥 按人员分组统计
- 🕐 导出时间与显示时间完全一致

### 3. 网络服务
- 🌐 HTTP API服务
- 📡 UDP广播服务发现
- 🔗 局域网设备自动发现
- 📱 支持移动设备连接

### 4. 系统监控
- 📊 实时服务状态显示
- 📝 详细系统日志记录
- 🔍 错误诊断和排查
- 📈 性能监控信息

## ⚠️ 常见问题解决

### 问题1：程序无法启动
**症状**：双击程序无反应或立即关闭

**解决方案**：
1. 右键选择"以管理员身份运行"
2. 检查杀毒软件是否阻止程序运行
3. 确保Windows系统已安装最新更新
4. 检查磁盘空间是否充足

### 问题2：端口被占用
**症状**：显示"端口已被占用"错误

**解决方案**：
```bash
# 使用不同端口启动
AttendanceSystem.exe --port 8001
```

### 问题3：网络功能异常
**症状**：无法访问HTTP服务或UDP广播不工作

**解决方案**：
1. 检查Windows防火墙设置
2. 确保端口8000和37020未被阻止
3. 在防火墙中添加程序例外
4. 检查网络连接状态

### 问题4：桌面程序不显示
**症状**：服务端启动但看不到桌面窗口

**解决方案**：
1. 检查任务栏是否有程序图标
2. 按Alt+Tab切换窗口
3. 检查显示器设置和分辨率
4. 尝试单独启动桌面程序：
   ```bash
   AttendanceSystem.exe --desktop-only
   ```

### 问题5：时间显示异常
**症状**：时间显示不是北京时间

**解决方案**：
1. 重新启动程序
2. 检查系统时区设置
3. 确保使用最新版本的程序
4. 如果问题持续，启用调试模式查看日志

## 🔍 调试和日志

### 查看详细日志
```bash
# 启动调试模式
AttendanceSystem.exe --debug
```

### 日志信息说明
- **INFO**: 正常操作信息
- **WARNING**: 警告信息（不影响运行）
- **ERROR**: 错误信息（需要处理）
- **DEBUG**: 详细调试信息

### 常见日志消息
```
✅ "服务端线程启动成功" - 服务端正常启动
✅ "桌面程序界面已显示" - 桌面程序正常显示
✅ "UDP广播服务启动成功" - 网络服务正常
❌ "端口已被占用" - 需要更换端口
❌ "数据库连接失败" - 检查数据文件
```

## 🛡️ 安全和防火墙设置

### Windows防火墙配置
1. 打开"Windows Defender 防火墙"
2. 点击"允许应用或功能通过Windows Defender防火墙"
3. 点击"更改设置"
4. 点击"允许其他应用"
5. 浏览并选择`AttendanceSystem.exe`
6. 确保"专用"和"公用"都被勾选
7. 点击"确定"

### 端口开放
如果需要局域网访问，确保以下端口开放：
- **TCP 8000**: HTTP服务端口
- **UDP 37020**: 广播服务端口

## 📊 性能优化建议

### 系统性能
- 建议在SSD硬盘上运行以提高性能
- 确保有足够的内存（建议4GB以上）
- 定期清理temp目录中的旧数据文件

### 网络性能
- 使用有线网络连接以获得最佳性能
- 确保网络延迟较低（<100ms）
- 避免在网络高峰期进行大量数据同步

## 📞 技术支持

### 获取帮助
如果遇到问题，请按以下步骤获取帮助：

1. **查看控制台日志**：程序运行时的详细信息
2. **启用调试模式**：`AttendanceSystem.exe --debug`
3. **检查网络连接**：确保网络正常
4. **重启程序**：关闭后重新启动
5. **重启计算机**：解决系统级问题

### 收集诊断信息
如需技术支持，请提供：
- 操作系统版本（Windows版本）
- 程序启动参数
- 控制台错误日志
- 网络配置信息
- 问题复现步骤

## 📋 版本信息

- **程序版本**: 1.0.0
- **构建日期**: 2025-07-24
- **文件大小**: 113.8 MB
- **支持系统**: Windows 10/11 x64
- **Python版本**: 3.11.x

## 🎉 功能特色

### ✅ 已修复的问题
- 时区显示异常问题
- 启动后时间自动变化问题
- Excel导出时间不一致问题
- 定时器触发时区变化问题

### ✅ 新增功能
- 统一的时区处理机制
- 智能的打包环境检测
- 优化的启动流程
- 完善的错误处理

### ✅ 性能改进
- 更快的启动速度
- 更低的内存占用
- 更稳定的网络服务
- 更准确的时间显示

---

**🎯 快速提醒**：
- 双击`AttendanceSystem.exe`即可启动
- 所有时间显示均为北京时间（UTC+8）
- 默认HTTP端口为8000
- 遇到问题请查看控制台日志

**📧 如有疑问，请查看控制台输出的详细日志信息。**
