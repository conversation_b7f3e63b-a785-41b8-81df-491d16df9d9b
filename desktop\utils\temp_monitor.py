"""
temp目录监控器模块
监控temp目录的文件变化，用于同步按钮状态管理
"""
import os
import time
from pathlib import Path
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, QObject
from typing import Set
import logging

class TempDirectoryMonitor(QThread):
    """temp目录监控线程"""
    
    # 信号定义
    files_added = pyqtSignal(list)      # 文件添加信号
    files_removed = pyqtSignal(list)    # 文件移除信号
    directory_empty = pyqtSignal()      # 目录为空信号
    directory_not_empty = pyqtSignal()  # 目录非空信号
    error_occurred = pyqtSignal(str)    # 错误信号
    
    def __init__(self, temp_dir_path: str, parent=None):
        super().__init__(parent)
        self.temp_dir_path = Path(temp_dir_path)
        self.is_monitoring = False
        self.check_interval = 1.0  # 检查间隔（秒）
        self.current_files: Set[str] = set()
        self.logger = logging.getLogger(__name__)
        
    def start_monitoring(self):
        """开始监控"""
        self.is_monitoring = True
        if not self.isRunning():
            self.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.isRunning():
            self.quit()
            self.wait(3000)  # 等待最多3秒
    
    def run(self):
        """监控线程主循环"""
        try:
            # 确保目录存在
            self.temp_dir_path.mkdir(parents=True, exist_ok=True)
            
            # 初始化当前文件列表
            self._update_current_files()
            
            while self.is_monitoring:
                try:
                    self._check_directory_changes()
                    self.msleep(int(self.check_interval * 1000))
                except Exception as e:
                    self.error_occurred.emit(f"监控temp目录时发生错误: {e}")
                    self.msleep(5000)  # 错误时等待5秒再重试
                    
        except Exception as e:
            self.error_occurred.emit(f"启动temp目录监控失败: {e}")
    
    def _update_current_files(self):
        """更新当前文件列表"""
        try:
            if self.temp_dir_path.exists():
                new_files = set()
                for file_path in self.temp_dir_path.iterdir():
                    if file_path.is_file():
                        new_files.add(file_path.name)
                self.current_files = new_files
            else:
                self.current_files = set()
        except Exception as e:
            self.logger.error(f"更新文件列表失败: {e}")
    
    def _check_directory_changes(self):
        """检查目录变化"""
        try:
            if not self.temp_dir_path.exists():
                return

            # 获取当前文件列表
            new_files = set()
            for file_path in self.temp_dir_path.iterdir():
                if file_path.is_file():
                    new_files.add(file_path.name)

            # 检查变化
            added_files = new_files - self.current_files
            removed_files = self.current_files - new_files

            # 调试日志
            if added_files or removed_files:
                self.logger.debug(f"目录变化检测: 当前文件={len(self.current_files)}, 新文件={len(new_files)}")
                self.logger.debug(f"添加的文件: {list(added_files)}")
                self.logger.debug(f"删除的文件: {list(removed_files)}")

            # 发送信号
            if added_files:
                self.logger.info(f"检测到新文件: {list(added_files)}")
                self.files_added.emit(list(added_files))

            if removed_files:
                self.logger.info(f"检测到删除文件: {list(removed_files)}")
                self.files_removed.emit(list(removed_files))

            # 检查目录是否为空
            was_empty = len(self.current_files) == 0
            is_empty = len(new_files) == 0

            if was_empty and not is_empty:
                self.logger.info(f"目录状态变化: 空 -> 非空 (文件数: {len(new_files)})")
                self.directory_not_empty.emit()
            elif not was_empty and is_empty:
                self.logger.info(f"目录状态变化: 非空 -> 空")
                self.directory_empty.emit()

            # 更新当前文件列表
            self.current_files = new_files

        except Exception as e:
            self.logger.error(f"检查目录变化失败: {e}")
            self.error_occurred.emit(f"检查目录变化失败: {e}")
    
    def clear_directory(self):
        """清空目录"""
        try:
            if self.temp_dir_path.exists():
                for file_path in self.temp_dir_path.iterdir():
                    if file_path.is_file():
                        file_path.unlink()
                        self.logger.info(f"删除文件: {file_path}")
                self.logger.info(f"已清空temp目录: {self.temp_dir_path}")
                return True
        except Exception as e:
            self.logger.error(f"清空temp目录失败: {e}")
            return False
        return True

class SyncButtonController(QObject):
    """同步按钮控制器"""
    
    # 信号定义
    button_state_changed = pyqtSignal(bool, str)  # (enabled, text)
    sync_completed = pyqtSignal()  # 同步完成信号
    
    def __init__(self, temp_dir_path: str, parent=None):
        super().__init__(parent)
        self.temp_monitor = TempDirectoryMonitor(temp_dir_path, self)
        self.is_syncing = False
        self.logger = logging.getLogger(__name__)
        
        # 连接信号
        self.temp_monitor.directory_empty.connect(self._on_directory_empty)
        self.temp_monitor.directory_not_empty.connect(self._on_directory_not_empty)
        self.temp_monitor.files_added.connect(self._on_files_added)
        self.temp_monitor.error_occurred.connect(self._on_monitor_error)
        
    def start_monitoring(self):
        """开始监控"""
        self.temp_monitor.start_monitoring()
        
    def stop_monitoring(self):
        """停止监控"""
        self.temp_monitor.stop_monitoring()
        
    def start_sync(self):
        """开始同步"""
        try:
            # 设置同步状态
            self.is_syncing = True
            self.logger.info(f"开始同步监控，设置is_syncing=True")
            return True
        except Exception as e:
            self.logger.error(f"开始同步失败: {e}")
            return False
    
    def _on_directory_empty(self):
        """目录为空时的处理"""
        self.logger.debug(f"目录为空信号触发，is_syncing={self.is_syncing}")
        if self.is_syncing:
            # 同步过程中目录为空是正常的
            self.logger.debug("同步过程中目录为空，正常状态")
        else:
            self.logger.debug("非同步状态下目录为空")
    
    def _on_directory_not_empty(self):
        """目录非空时的处理"""
        self.logger.info(f"目录非空信号触发，is_syncing={self.is_syncing}")
        if self.is_syncing:
            # 同步过程中出现文件，说明同步完成
            self.logger.info("同步过程中检测到文件，开始完成同步流程")
            self.is_syncing = False
            self.logger.info("发送按钮状态变化信号: enabled=True, text='同步'")
            self.button_state_changed.emit(True, "同步")
            self.logger.info("发送同步完成信号")
            self.sync_completed.emit()
            self.logger.info("同步完成处理结束")
        else:
            self.logger.debug("非同步状态下目录变为非空，忽略")
    
    def _on_files_added(self, files):
        """文件添加时的处理"""
        self.logger.info(f"文件添加信号触发: {files}, is_syncing={self.is_syncing}")
        if self.is_syncing:
            # 如果正在同步中，说明同步完成
            self.logger.info("同步状态下检测到文件添加，调用目录非空处理")
            self._on_directory_not_empty()
        else:
            self.logger.debug("非同步状态下文件添加，忽略")
    
    def _on_monitor_error(self, error_message):
        """监控错误处理"""
        self.logger.error(f"temp目录监控错误: {error_message}")
        # 发生错误时恢复按钮状态
        if self.is_syncing:
            self.is_syncing = False
            self.button_state_changed.emit(True, "同步")
    
    def reset_state(self):
        """重置状态"""
        self.is_syncing = False
        self.button_state_changed.emit(True, "同步")
