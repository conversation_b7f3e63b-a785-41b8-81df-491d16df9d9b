import asyncio
from PyQt6.QtCore import QObject, pyqtSignal
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path
import json
import os

class DeviceController(QObject):
    devices_updated = pyqtSignal(list)

    def __init__(self):
        super().__init__()
        self.devices = {}  # 设备缓存

        # 使用路径管理器获取配置文件路径
        try:
            from config.path_manager import get_path_manager
            path_manager = get_path_manager()
            self.local_devices_file = path_manager.get_config_file_path("devices.json")
        except ImportError:
            # 备用方案：使用硬编码路径
            self.local_devices_file = Path(__file__).parent.parent.parent / "temp_cfg" / "devices.json"
            # 确保temp_cfg目录存在
            temp_cfg_dir = self.local_devices_file.parent
            if not temp_cfg_dir.exists():
                temp_cfg_dir.mkdir(parents=True, exist_ok=True)

        # 如果设备文件不存在，创建一个空的设备列表文件
        if not self.local_devices_file.exists():
            self._save_local_devices([])
    
    def _save_local_devices(self, devices):
        """保存设备列表到本地文件"""
        with open(self.local_devices_file, 'w', encoding='utf-8') as f:
            json.dump(devices, f, ensure_ascii=False, indent=2)
    
    def _load_local_devices(self):
        """从本地文件加载设备列表"""
        if not self.local_devices_file.exists():
            return []
        
        try:
            with open(self.local_devices_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载设备列表失败: {e}")
            return []
        
    async def get_device_list(self) -> List[Dict]:
        """获取设备列表 - 从本地文件读取，不连接服务器"""
        try:
            devices = self._load_local_devices()
            self.devices = {d["device_id"]: d for d in devices}
            self.devices_updated.emit(devices)  # 发射信号
            return devices
        except Exception as e:
            print(f"获取设备列表失败: {e}")
            self.devices_updated.emit([])  # 发射空列表信号
            return []
                
    async def get_device_status(self, device_id: str) -> Optional[Dict]:
        """获取设备状态 - 从本地缓存获取，不连接服务器"""
        try:
            device = self.devices.get(device_id)
            if device:
                # 如果设备存在，返回一个模拟的状态
                status = {
                    "status": "offline",  # 默认离线状态
                    "last_seen": datetime.now().isoformat(),
                    "battery": device.get("battery", 0),
                    "version": device.get("version", "unknown")
                }
                
                # 更新设备信息
                if device_id in self.devices:
                    self.devices[device_id].update(status)
                
                return status
            return None
        except Exception as e:
            print(f"获取设备状态失败: {e}")
            return None
                
    async def update_device_settings(self, device_id: str, settings: Dict) -> bool:
        """更新设备设置 - 更新本地缓存，不连接服务器"""
        try:
            if device_id in self.devices:
                # 更新设备设置
                if "settings" not in self.devices[device_id]:
                    self.devices[device_id]["settings"] = {}
                
                self.devices[device_id]["settings"].update(settings)
                
                # 保存到本地文件
                self._save_local_devices(list(self.devices.values()))
                
                return True
            return False
        except Exception as e:
            print(f"更新设备设置失败: {e}")
            return False
                
    def update_device_list(self):
        """更新设备列表（在单独的线程中运行）"""
        # 注意：直接在UI线程中运行asyncio循环不是最佳实践
        # 更好的方法是使用QThread来处理异步任务
        asyncio.run(self.get_device_list())
            
    def get_cached_device(self, device_id: str) -> Optional[Dict]:
        """获取缓存的设备信息"""
        return self.devices.get(device_id)
        
    def get_cached_devices(self) -> List[Dict]:
        """获取所有缓存的设备信息"""
        return list(self.devices.values()) 