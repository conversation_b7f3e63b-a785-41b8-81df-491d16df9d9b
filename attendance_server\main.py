"""
考勤系统服务端 FastAPI 应用
"""
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .config import API, LOGGING
from .db.database import init_db
from .api import heartbeat, device
from .api.upload import router as upload_router

# 配置日志
import os
import sys
from pathlib import Path

# 确保日志目录存在
log_file = Path(LOGGING['file'])
log_file.parent.mkdir(parents=True, exist_ok=True)

# 在打包环境中输出日志配置信息
if getattr(sys, 'frozen', False):
    print(f"[打包环境] 服务端日志配置:")
    print(f"  日志文件路径: {log_file}")
    print(f"  日志目录: {log_file.parent}")
    print(f"  日志目录是否存在: {log_file.parent.exists()}")
    print(f"  日志文件是否可写: {os.access(log_file.parent, os.W_OK)}")

    # 输出环境变量信息
    if 'ATTENDANCE_LOGS_DIR' in os.environ:
        print(f"  环境变量ATTENDANCE_LOGS_DIR: {os.environ['ATTENDANCE_LOGS_DIR']}")
    if 'ATTENDANCE_APP_PATH' in os.environ:
        print(f"  环境变量ATTENDANCE_APP_PATH: {os.environ['ATTENDANCE_APP_PATH']}")
else:
    print(f"[开发环境] 服务端日志文件: {log_file}")

# 清除现有的日志配置
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)

# 配置日志处理器
logging.basicConfig(
    level=getattr(logging, LOGGING['level'].upper()),
    format=LOGGING['format'],
    handlers=[
        logging.FileHandler(LOGGING['file'], encoding='utf-8'),
        logging.StreamHandler()  # 同时输出到控制台
    ],
    force=True  # 强制重新配置
)

# 确保所有相关的logger都使用我们的配置
logger = logging.getLogger(__name__)
heartbeat_logger = logging.getLogger('attendance_server.api.heartbeat')
heartbeat_logger.setLevel(getattr(logging, LOGGING['level'].upper()))

# 创建 FastAPI 应用
app = FastAPI(
    title="考勤系统服务端",
    description="考勤系统服务端API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(heartbeat.router, prefix=API['prefix'])
app.include_router(device.router, prefix=API['prefix'])
app.include_router(upload_router, prefix="/api", tags=["upload"])

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    try:
        # 初始化数据库
        init_db()
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("正在启动考勤系统服务端...")
    logger.info("考勤系统服务端启动完成")

@app.get("/")
async def root():
    """API根路径"""
    return {
        "name": "考勤系统服务端",
        "version": "1.0.0",
        "status": "running"
    }

# 如果直接运行此文件
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "attendance_server.main:app",
        host=API['host'],
        port=API['port'],
        reload=True,
        log_level="info"
    ) 