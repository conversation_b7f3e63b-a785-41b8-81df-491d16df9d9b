# 心跳接口修改总结

## 修改概述
根据需求，修改了考勤系统服务端的心跳接口处理逻辑，添加了对 `temp_cfg/update.txt` 文件的检查功能。

## 修改的文件
- `attendance_server/api/heartbeat.py`

## 具体修改内容

### 1. 添加的导入和常量
```python
from pathlib import Path

# 获取项目根目录路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
UPDATE_FILE_PATH = PROJECT_ROOT / "temp_cfg" / "update.txt"
```

### 2. 新增的辅助函数
```python
def _check_and_handle_update_file() -> str:
    """
    检查 temp_cfg/update.txt 文件是否存在，如果存在则删除并返回 'upload'，否则返回默认消息
    
    Returns:
        str: 如果文件存在返回 'upload'，否则返回 'Heartbeat received'
    """
```

### 3. 修改的心跳处理函数
在 `handle_heartbeat` 函数中添加了文件检查逻辑：
```python
# 检查并处理更新文件
message = _check_and_handle_update_file()

logger.info(f"处理设备 {device_id} 心跳请求，响应消息: {message}")

return {
    "device_id": device_id,
    "status": "success",
    "message": message,  # 动态设置消息内容
    "sync_status": device.sync_status
}
```

## 功能说明

### 工作流程
1. 当客户端发送心跳请求时，服务端首先进行常规的设备验证和心跳更新
2. 然后调用 `_check_and_handle_update_file()` 函数检查 `temp_cfg/update.txt` 文件
3. 如果文件存在：
   - 记录日志信息
   - 删除该文件
   - 返回 `message: "upload"`
4. 如果文件不存在：
   - 返回 `message: "Heartbeat received"`

### 错误处理
- 文件删除失败时，仍然返回 "upload" 消息（因为文件确实存在）
- 任何异常都会被捕获，确保心跳响应正常返回
- 所有关键操作都有详细的日志记录

### 响应格式
心跳响应保持原有的JSON格式：
```json
{
    "device_id": "设备ID",
    "status": "success",
    "message": "upload" 或 "Heartbeat received",
    "sync_status": "设备同步状态"
}
```

## 测试验证
已通过以下测试验证修改的正确性：
1. ✅ 文件不存在时返回正常心跳消息
2. ✅ 文件存在时返回 "upload" 消息并删除文件
3. ✅ 文件删除后再次检查返回正常消息
4. ✅ 路径解析正确
5. ✅ 错误处理机制正常
6. ✅ 集成测试通过

## 安全性和稳定性
- 使用 `pathlib.Path` 进行安全的路径操作
- 完善的异常处理确保服务稳定性
- 详细的日志记录便于问题排查
- 即使文件操作失败也不影响心跳响应的正常返回

## 兼容性
- 保持了原有的API接口格式
- 向后兼容，不影响现有客户端
- 只在 `message` 字段的值上有动态变化

## 使用方法
1. 当需要触发客户端上传数据时，在项目根目录的 `temp_cfg/` 目录下创建 `update.txt` 文件
2. 客户端下次发送心跳请求时，会收到 `message: "upload"` 的响应
3. 服务端会自动删除该文件，避免重复触发
