from fastapi import APIRouter, UploadFile, File, Form
from fastapi.responses import JSONResponse
import os
from pathlib import Path

router = APIRouter()

# 导入路径管理器
try:
    from config.path_manager import get_path_manager
    path_manager = get_path_manager()
    USE_PATH_MANAGER = True
    print("[上传API] 成功导入路径管理器")
except ImportError:
    # 备用方案：使用硬编码路径
    USE_PATH_MANAGER = False
    print("[上传API] 警告: 无法导入路径管理器，使用备用路径配置")

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    device_id: str = Form(...)
):
    try:
        # 获取保存路径
        if USE_PATH_MANAGER:
            # 使用路径管理器获取temp目录
            save_dir = path_manager.get_temp_dir()
            print(f"[上传API] 使用路径管理器获取保存目录: {save_dir}")
        else:
            # 备用方案：使用硬编码路径
            project_root = Path(__file__).parent.parent.parent
            save_dir = project_root / "temp"
            print(f"[上传API] 使用备用方案保存目录: {save_dir}")

        # 使用device_id作为文件名
        filename = f"{device_id}.db"
        save_path = save_dir / filename

        # 确保temp目录存在
        save_dir.mkdir(parents=True, exist_ok=True)
        print(f"[上传API] 保存文件到: {save_path}")

        # 保存文件
        content = await file.read()
        with open(save_path, "wb") as f:
            f.write(content)

        print(f"[上传API] 文件保存成功: {save_path}, 大小: {len(content)} bytes")
        return {
            "message": "ok",
            "device_id": device_id,
            "filename": filename,
            "file_size": len(content),
            "save_path": str(save_path)
        }
    except Exception as e:
        error_msg = f"Upload failed: {str(e)}"
        print(f"[上传API] 错误: {error_msg}")
        return JSONResponse(
            status_code=500,
            content={"message": error_msg}
        )