#!/usr/bin/env python3
"""
实时UDP广播监控工具
用于持续监控考勤系统的UDP广播服务状态
"""

import sys
import time
import socket
import threading
from pathlib import Path
from datetime import datetime
import signal

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class UDPBroadcastMonitor:
    """UDP广播监控器"""
    
    def __init__(self, broadcast_port=37020):
        self.broadcast_port = broadcast_port
        self.running = False
        self.socket = None
        self.monitor_thread = None
        self.received_messages = []
        self.last_message_time = None
        self.message_count = 0
        
    def start_monitoring(self):
        """开始监控"""
        if self.running:
            print("监控器已在运行中")
            return False
        
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind(('', self.broadcast_port))
            self.socket.settimeout(1.0)
            
            self.running = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            print(f"🎯 UDP广播监控器已启动")
            print(f"   监听端口: {self.broadcast_port}")
            print(f"   启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("   按 Ctrl+C 停止监控")
            print("-" * 60)
            
            return True
            
        except Exception as e:
            print(f"❌ 启动监控器失败: {e}")
            return False
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2)
        
        print(f"\n📊 监控统计:")
        print(f"   总接收消息: {self.message_count} 条")
        print(f"   监控时长: {self._get_monitor_duration()}")
        print(f"   停止时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🛑 UDP广播监控器已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        start_time = time.time()
        last_stats_time = start_time
        
        while self.running:
            try:
                # 接收UDP数据
                data, addr = self.socket.recvfrom(1024)
                message = data.decode('utf-8')
                current_time = datetime.now()
                
                # 记录消息
                self.received_messages.append({
                    'time': current_time,
                    'addr': addr,
                    'message': message
                })
                self.last_message_time = current_time
                self.message_count += 1
                
                # 显示消息
                time_str = current_time.strftime('%H:%M:%S')
                print(f"[{time_str}] 📡 {addr[0]}:{addr[1]} -> {message}")
                
                # 每30秒显示一次统计信息
                if time.time() - last_stats_time >= 30:
                    self._show_stats()
                    last_stats_time = time.time()
                
            except socket.timeout:
                # 检查是否长时间没有收到消息
                if self.last_message_time:
                    silence_duration = (datetime.now() - self.last_message_time).total_seconds()
                    if silence_duration > 10:  # 超过10秒没有消息
                        print(f"⚠️  [{datetime.now().strftime('%H:%M:%S')}] 已 {silence_duration:.0f} 秒未收到广播消息")
                continue
            except Exception as e:
                if self.running:
                    print(f"❌ 接收数据时出错: {e}")
                break
    
    def _show_stats(self):
        """显示统计信息"""
        current_time = datetime.now()
        print(f"\n📈 [{current_time.strftime('%H:%M:%S')}] 统计信息:")
        print(f"   累计消息: {self.message_count} 条")
        print(f"   监控时长: {self._get_monitor_duration()}")
        
        if self.last_message_time:
            last_msg_ago = (current_time - self.last_message_time).total_seconds()
            print(f"   最后消息: {last_msg_ago:.0f} 秒前")
        
        # 分析最近的消息间隔
        if len(self.received_messages) >= 2:
            recent_messages = self.received_messages[-5:]  # 最近5条消息
            intervals = []
            for i in range(1, len(recent_messages)):
                interval = (recent_messages[i]['time'] - recent_messages[i-1]['time']).total_seconds()
                intervals.append(interval)
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                print(f"   平均间隔: {avg_interval:.1f} 秒")
        
        print("-" * 60)
    
    def _get_monitor_duration(self):
        """获取监控持续时间"""
        if not hasattr(self, '_start_time'):
            self._start_time = time.time()
        
        duration = time.time() - self._start_time
        minutes = int(duration // 60)
        seconds = int(duration % 60)
        
        if minutes > 0:
            return f"{minutes}分{seconds}秒"
        else:
            return f"{seconds}秒"

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在停止监控...")
    global monitor
    if monitor:
        monitor.stop_monitoring()
    sys.exit(0)

def test_network_connectivity():
    """测试网络连接性"""
    print("🔍 测试网络连接性...")
    
    try:
        from desktop.utils.network_utils import get_preferred_ip_address, get_all_local_ip_addresses
        
        preferred_ip = get_preferred_ip_address()
        all_ips = get_all_local_ip_addresses()
        
        print(f"   优先IP地址: {preferred_ip}")
        print(f"   所有IP地址: {all_ips}")
        
        # 测试UDP端口是否可用
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            test_socket.bind(('', 37020))
            print("   ✅ UDP端口37020可用")
            test_socket.close()
            return True
        except OSError as e:
            print(f"   ❌ UDP端口37020不可用: {e}")
            test_socket.close()
            return False
            
    except Exception as e:
        print(f"   ❌ 网络连接性测试失败: {e}")
        return False

def show_help():
    """显示帮助信息"""
    print("UDP广播监控工具使用说明:")
    print("")
    print("功能:")
    print("  - 实时监控UDP广播消息")
    print("  - 显示消息来源和内容")
    print("  - 统计广播频率和间隔")
    print("  - 检测广播中断")
    print("")
    print("使用方法:")
    print("  python monitor_udp_broadcast.py [选项]")
    print("")
    print("选项:")
    print("  -h, --help     显示此帮助信息")
    print("  -p, --port     指定监听端口 (默认: 37020)")
    print("  -t, --test     仅测试网络连接性")
    print("")
    print("示例:")
    print("  python monitor_udp_broadcast.py")
    print("  python monitor_udp_broadcast.py -p 37020")
    print("  python monitor_udp_broadcast.py --test")

def main():
    """主函数"""
    global monitor
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='UDP广播监控工具', add_help=False)
    parser.add_argument('-h', '--help', action='store_true', help='显示帮助信息')
    parser.add_argument('-p', '--port', type=int, default=37020, help='监听端口')
    parser.add_argument('-t', '--test', action='store_true', help='仅测试网络连接性')
    
    args = parser.parse_args()
    
    if args.help:
        show_help()
        return
    
    print("🚀 UDP广播监控工具")
    print("=" * 60)
    
    # 测试网络连接性
    if not test_network_connectivity():
        print("❌ 网络连接性测试失败，请检查网络配置")
        if not args.test:
            print("提示: 使用 -t 参数仅进行网络测试")
        return
    
    if args.test:
        print("✅ 网络连接性测试通过")
        return
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建并启动监控器
    monitor = UDPBroadcastMonitor(broadcast_port=args.port)
    
    if monitor.start_monitoring():
        try:
            # 保持主线程运行
            while monitor.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            monitor.stop_monitoring()
    else:
        print("❌ 无法启动UDP广播监控器")
        sys.exit(1)

if __name__ == "__main__":
    monitor = None
    main()
