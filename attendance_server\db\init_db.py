import sqlite3
from .crud import DB_PATH

def init_db():
    """初始化数据库表"""
    conn = sqlite3.connect(DB_PATH)
    cur = conn.cursor()
    
    try:
        # 设备表
        cur.execute('''
        CREATE TABLE IF NOT EXISTS devices (
            device_id TEXT PRIMARY KEY,
            status TEXT,
            last_heartbeat TEXT,
            sync_status TEXT DEFAULT 'completed',
            last_sync TEXT
        )
        ''')
        
        # 考勤记录表
        cur.execute('''
        CREATE TABLE IF NOT EXISTS attendance_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            device_id TEXT,
            sfz_name TEXT,
            sfz_id TEXT,
            sfz_group TEXT,
            enter_time TEXT,
            md5sum TEXT UNIQUE,
            FOREIGN KEY (device_id) REFERENCES devices(device_id)
        )
        ''')
        
        # 系统日志表
        cur.execute('''
        CREATE TABLE IF NOT EXISTS system_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            level TEXT,
            message TEXT
        )
        ''')
        
        conn.commit()
        
    finally:
        conn.close()

if __name__ == '__main__':
    init_db() 