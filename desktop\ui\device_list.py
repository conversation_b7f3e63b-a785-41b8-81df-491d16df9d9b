from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView
)
from PyQt6.QtCore import pyqtSignal, Qt, QDateTime
from typing import Optional
from datetime import datetime as py_datetime, timezone, timedelta # 导入 timedelta

class DeviceListWidget(QWidget):
    # 自定义信号
    device_selected = pyqtSignal(str)  # 设备选择信号
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["设备ID", "同步状态", "最后心跳", "在线"])
        
        # 设置表格属性
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
        # 连接信号
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.table)
        
    def update_device_list(self, devices: list):
        """更新设备列表
        
        Args:
            devices: 设备列表，每个设备是一个字典
        """
        self.table.blockSignals(True) # 更新期间阻塞信号
        self.table.setRowCount(len(devices))
        
        for row, device in enumerate(devices):
            # 设备ID
            id_item = QTableWidgetItem(device.get("device_id", "N/A"))
            id_item.setFlags(id_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(row, 0, id_item)
            
            # 同步状态
            sync_status = device.get("sync_status", "unknown")
            status_item = QTableWidgetItem(sync_status)
            status_item.setFlags(status_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(row, 1, status_item)
            
            # 最后心跳时间
            last_heartbeat = device.get("last_heartbeat")
            if last_heartbeat:
                try:
                    # 解析时间并转换为北京时间
                    py_dt_naive = py_datetime.fromisoformat(last_heartbeat)
                    py_dt_utc = py_dt_naive.replace(tzinfo=timezone.utc)
                    py_dt_local = py_dt_utc + timedelta(hours=8)
                    qt_dt_local = QDateTime(py_dt_local)
                    heartbeat_str = qt_dt_local.toString("HH:mm:ss")
                except (ValueError, TypeError):
                    heartbeat_str = "Invalid Time"
            else:
                heartbeat_str = "N/A"
            sync_item = QTableWidgetItem(heartbeat_str)
            sync_item.setFlags(sync_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(row, 2, sync_item)
            
            # 在线状态
            online_text = "在线" if device.get("is_online") else "离线"
            online_item = QTableWidgetItem(online_text)
            online_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            online_item.setFlags(online_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(row, 3, online_item)
            
        self.table.blockSignals(False) # 恢复信号
    
    def get_selected_devices(self) -> list:
        """获取选中的设备ID列表"""
        selected_rows = set(item.row() for item in self.table.selectedItems())
        devices = []
        for row in selected_rows:
            item = self.table.item(row, 0)
            if item:
                devices.append(item.text())
        return devices
    
    def get_current_device(self) -> Optional[str]:
        """获取当前选中的设备ID"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            item = self.table.item(current_row, 0)
            if item:
                return item.text()
        return None
    
    def on_selection_changed(self):
        """选择变更事件"""
        current_device = self.get_current_device()
        if current_device:
            self.device_selected.emit(current_device) 