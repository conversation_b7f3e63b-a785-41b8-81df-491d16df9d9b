from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QTextEdit, QHBoxLayout,
    QPushButton, QComboBox, QLabel
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QTextCursor, QColor, QTextCharFormat
from datetime import datetime, timezone, timedelta
from ..utils.time_utils import get_current_datetime_string

class LogViewerWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = self.create_toolbar()
        layout.addLayout(toolbar)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)
        layout.addWidget(self.log_text)
        
        # 设置日志颜色
        self.log_colors = {
            "信息": QColor(0, 0, 0),      # 黑色
            "警告": QColor(255, 165, 0),  # 橙色
            "错误": QColor(255, 0, 0),    # 红色
            "成功": QColor(0, 128, 0)     # 绿色
        }
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QHBoxLayout()
        
        # 日志级别筛选
        toolbar.addWidget(QLabel("日志级别:"))
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "信息", "警告", "错误", "成功"])
        self.level_combo.currentTextChanged.connect(self.filter_logs)
        toolbar.addWidget(self.level_combo)
        
        # 清空按钮
        self.clear_button = QPushButton("清空")
        self.clear_button.clicked.connect(self.clear_logs)
        toolbar.addWidget(self.clear_button)
        
        # 导出按钮
        self.export_button = QPushButton("导出")
        self.export_button.clicked.connect(self.export_logs)
        toolbar.addWidget(self.export_button)
        
        toolbar.addStretch()
        return toolbar
        
    def add_log(self, level: str, message: str):
        """添加日志
        
        Args:
            level: 日志级别，可选值：信息、警告、错误、成功
            message: 日志消息
        """
        # 获取当前时间（使用北京时间）
        timestamp = get_current_datetime_string()
        
        # 创建日志文本格式
        format = QTextCharFormat()
        format.setForeground(self.log_colors.get(level, QColor(0, 0, 0)))
        
        # 添加日志
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        
        # 如果不是第一行，添加换行
        if not self.log_text.toPlainText() == "":
            cursor.insertText("\n")
            
        # 插入日志
        log_text = f"[{timestamp}] [{level}] {message}"
        cursor.insertText(log_text, format)
        
        # 滚动到底部
        self.log_text.setTextCursor(cursor)
        self.log_text.ensureCursorVisible()
        
    def filter_logs(self):
        """根据日志级别筛选日志"""
        selected_level = self.level_combo.currentText()
        if selected_level == "全部":
            return
            
        # 获取所有日志
        logs = self.log_text.toPlainText().split("\n")
        filtered_logs = []
        
        for log in logs:
            if f"[{selected_level}]" in log:
                filtered_logs.append(log)
                
        # 更新显示
        self.log_text.clear()
        self.log_text.setPlainText("\n".join(filtered_logs))
        
    def clear_logs(self):
        """清空日志"""
        self.log_text.clear()
        
    def export_logs(self):
        """导出日志"""
        # TODO: 实现日志导出功能
        pass 