#!/usr/bin/env python3
"""
创建测试数据脚本
用于生成考勤系统的测试数据库文件
"""

import sqlite3
import os
from datetime import datetime, timedelta
from pathlib import Path

def create_test_database(db_path, device_id, num_records=10):
    """创建测试数据库文件"""
    
    # 创建数据库连接
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建考勤记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS attendance_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            device_id TEXT NOT NULL,
            user_id TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            record_type TEXT NOT NULL,
            verification_mode TEXT NOT NULL,
            work_code INTEGER DEFAULT 0
        )
    ''')
    
    # 生成测试数据
    base_time = datetime.now()
    
    for i in range(num_records):
        # 生成时间戳（过去几天的随机时间）
        days_ago = i % 7  # 最近7天
        hours_offset = (i * 2) % 24  # 不同的小时
        test_time = base_time - timedelta(days=days_ago, hours=hours_offset)
        timestamp = int(test_time.timestamp())
        
        # 生成用户ID
        user_id = f"USER{(i % 5) + 1:03d}"  # USER001 到 USER005
        
        # 生成记录类型（上班/下班）
        record_type = "上班" if i % 2 == 0 else "下班"
        
        # 生成验证方式
        verification_modes = ["指纹", "人脸", "密码", "刷卡"]
        verification_mode = verification_modes[i % len(verification_modes)]
        
        # 插入记录
        cursor.execute('''
            INSERT INTO attendance_records 
            (device_id, user_id, timestamp, record_type, verification_mode, work_code)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (device_id, user_id, timestamp, record_type, verification_mode, 1))
    
    # 提交并关闭
    conn.commit()
    conn.close()
    
    print(f"已创建测试数据库: {db_path}")
    print(f"设备ID: {device_id}")
    print(f"记录数量: {num_records}")

def main():
    """主函数"""
    # 获取dist目录路径
    script_dir = Path(__file__).parent
    dist_dir = script_dir / "dist"
    temp_dir = dist_dir / "temp"
    
    # 确保temp目录存在
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建多个测试设备的数据库
    test_devices = [
        ("DEVICE001", 15),
        ("DEVICE002", 12),
        ("DEVICE003", 8)
    ]
    
    for device_id, num_records in test_devices:
        db_path = temp_dir / f"{device_id}.db"
        create_test_database(str(db_path), device_id, num_records)
    
    print(f"\n测试数据创建完成！")
    print(f"数据库文件位置: {temp_dir}")
    print(f"共创建 {len(test_devices)} 个设备的测试数据")
    
    # 列出创建的文件
    print("\n创建的文件:")
    for file_path in temp_dir.glob("*.db"):
        file_size = file_path.stat().st_size
        print(f"  {file_path.name} ({file_size} 字节)")

if __name__ == "__main__":
    main()
