"""
UDP广播服务模块
实现局域网内服务器自动发现功能
"""
import socket
import threading
import time
import logging
from typing import Optional, Callable
from .network_utils import get_preferred_ip_address

logger = logging.getLogger(__name__)

class UDPBroadcastService:
    """UDP广播服务类"""
    
    def __init__(self, server_port: int = 8000, broadcast_port: int = 37020, 
                 broadcast_interval: float = 5.0):
        """
        初始化UDP广播服务
        
        Args:
            server_port: 服务端HTTP端口
            broadcast_port: UDP广播端口
            broadcast_interval: 广播间隔（秒）
        """
        self.server_port = server_port
        self.broadcast_port = broadcast_port
        self.broadcast_interval = broadcast_interval
        self.broadcast_address = "***************"
        
        self._socket = None
        self._thread = None
        self._running = False
        self._lock = threading.Lock()
        
        # 回调函数
        self.on_start_callback: Optional[Callable[[str, int], None]] = None
        self.on_stop_callback: Optional[Callable[[], None]] = None
        self.on_error_callback: Optional[Callable[[str], None]] = None
        
    def set_callbacks(self, on_start=None, on_stop=None, on_error=None):
        """设置回调函数"""
        if on_start:
            self.on_start_callback = on_start
        if on_stop:
            self.on_stop_callback = on_stop
        if on_error:
            self.on_error_callback = on_error
    
    def start(self) -> bool:
        """
        启动UDP广播服务
        
        Returns:
            bool: 启动是否成功
        """
        with self._lock:
            if self._running:
                logger.warning("UDP广播服务已在运行")
                return True
            
            try:
                # 获取优先的IP地址
                preferred_ip = get_preferred_ip_address()
                if not preferred_ip:
                    error_msg = "无法获取合适的IP地址用于UDP广播"
                    logger.error(error_msg)
                    if self.on_error_callback:
                        self.on_error_callback(error_msg)
                    return False
                
                # 创建UDP socket
                self._socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                self._socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
                self._socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                
                # 设置socket超时，避免阻塞
                self._socket.settimeout(1.0)
                
                self._running = True
                
                # 启动广播线程
                self._thread = threading.Thread(
                    target=self._broadcast_loop,
                    args=(preferred_ip,),
                    daemon=True,
                    name="UDPBroadcastThread"
                )
                self._thread.start()
                
                logger.info(f"UDP广播服务启动成功，IP: {preferred_ip}:{self.server_port}, 广播端口: {self.broadcast_port}")
                
                if self.on_start_callback:
                    self.on_start_callback(preferred_ip, self.broadcast_port)
                
                return True
                
            except Exception as e:
                error_msg = f"启动UDP广播服务失败: {str(e)}"
                logger.error(error_msg)
                self._running = False
                if self._socket:
                    try:
                        self._socket.close()
                    except:
                        pass
                    self._socket = None
                
                if self.on_error_callback:
                    self.on_error_callback(error_msg)
                return False
    
    def stop(self):
        """停止UDP广播服务"""
        with self._lock:
            if not self._running:
                return
            
            logger.info("正在停止UDP广播服务...")
            self._running = False
            
            # 关闭socket
            if self._socket:
                try:
                    self._socket.close()
                except:
                    pass
                self._socket = None
            
            # 等待线程结束
            if self._thread and self._thread.is_alive():
                self._thread.join(timeout=2.0)
                if self._thread.is_alive():
                    logger.warning("UDP广播线程未能在超时时间内结束")
            
            self._thread = None
            
            logger.info("UDP广播服务已停止")
            
            if self.on_stop_callback:
                self.on_stop_callback()
    
    def is_running(self) -> bool:
        """检查服务是否正在运行"""
        return self._running
    
    def _broadcast_loop(self, server_ip: str):
        """广播循环（在独立线程中运行）"""
        message = f"SERVER_IP:{server_ip}:{self.server_port}"
        message_bytes = message.encode('utf-8')
        
        logger.debug(f"开始UDP广播循环，消息: {message}")
        
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self._running:
            try:
                if self._socket:
                    # 发送广播消息
                    self._socket.sendto(message_bytes, (self.broadcast_address, self.broadcast_port))
                    logger.debug(f"发送UDP广播: {message}")
                    consecutive_errors = 0  # 重置错误计数
                
                # 等待下次广播
                for _ in range(int(self.broadcast_interval * 10)):
                    if not self._running:
                        break
                    time.sleep(0.1)
                
            except socket.timeout:
                # 超时是正常的，继续循环
                continue
                
            except Exception as e:
                consecutive_errors += 1
                logger.warning(f"UDP广播发送失败 ({consecutive_errors}/{max_consecutive_errors}): {str(e)}")
                
                if consecutive_errors >= max_consecutive_errors:
                    error_msg = f"UDP广播连续失败{max_consecutive_errors}次，停止服务"
                    logger.error(error_msg)
                    if self.on_error_callback:
                        self.on_error_callback(error_msg)
                    break
                
                # 短暂等待后重试
                time.sleep(1.0)
        
        logger.debug("UDP广播循环结束")
    
    def get_status(self) -> dict:
        """获取服务状态信息"""
        return {
            'running': self._running,
            'server_port': self.server_port,
            'broadcast_port': self.broadcast_port,
            'broadcast_interval': self.broadcast_interval,
            'broadcast_address': self.broadcast_address,
            'thread_alive': self._thread.is_alive() if self._thread else False
        }
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.stop()
        except:
            pass
