"""
PyInstaller hook for PyQt6
确保PyQt6及其依赖被正确包含
"""

from PyInstaller.utils.hooks import collect_all, collect_submodules

# 收集PyQt6的所有模块
datas, binaries, hiddenimports = collect_all('PyQt6')

# 添加额外的隐藏导入
hiddenimports += [
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'PyQt6.QtWidgets',
    'PyQt6.QtNetwork',
    'PyQt6.QtSql',
    'PyQt6.sip',
    'sip',
]

# 收集PyQt6的所有子模块
hiddenimports += collect_submodules('PyQt6')
