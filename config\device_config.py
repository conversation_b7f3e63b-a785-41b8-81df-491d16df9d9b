#!/usr/bin/env python3
"""
设备配置模块
管理客户端总数等设备相关配置
"""

import os
from pathlib import Path
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class DeviceConfig:
    """设备配置管理类"""
    
    _instance: Optional['DeviceConfig'] = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_config()
            DeviceConfig._initialized = True
    
    def _setup_config(self):
        """设置配置"""
        try:
            # 尝试使用路径管理器
            from config.path_manager import get_path_manager
            self.path_manager = get_path_manager()
            self.dev_sum_file = self.path_manager.get_config_file_path("dev_sum.txt")
            self.use_path_manager = True
            logger.debug("使用路径管理器获取配置文件路径")
        except ImportError:
            # 备用方案：使用相对路径
            self.dev_sum_file = Path("temp_cfg") / "dev_sum.txt"
            self.use_path_manager = False
            logger.warning("无法导入路径管理器，使用备用路径配置")
        
        # 初始化客户端总数
        self._client_total = self._load_client_total()
    
    def _load_client_total(self) -> int:
        """从文件中加载客户端总数"""
        try:
            if self.dev_sum_file.exists():
                with open(self.dev_sum_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content.isdigit():
                        total = int(content)
                        logger.info(f"从文件加载客户端总数: {total}")
                        return total
                    else:
                        logger.warning(f"dev_sum.txt文件内容无效: {content}，使用默认值1")
                        return self._create_default_file()
            else:
                logger.info("dev_sum.txt文件不存在，创建默认文件")
                return self._create_default_file()
        except Exception as e:
            logger.error(f"读取客户端总数文件失败: {e}")
            return self._create_default_file()
    
    def _create_default_file(self) -> int:
        """创建默认的dev_sum.txt文件"""
        try:
            # 确保目录存在
            self.dev_sum_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入默认值
            with open(self.dev_sum_file, 'w', encoding='utf-8') as f:
                f.write("1")
            
            logger.info(f"创建默认dev_sum.txt文件: {self.dev_sum_file}")
            return 1
        except Exception as e:
            logger.error(f"创建默认dev_sum.txt文件失败: {e}")
            return 1
    
    def get_client_total(self) -> int:
        """获取客户端总数"""
        return self._client_total
    
    def set_client_total(self, total: int) -> bool:
        """设置客户端总数并保存到文件"""
        try:
            if total <= 0:
                logger.error(f"客户端总数必须大于0: {total}")
                return False
            
            # 确保目录存在
            self.dev_sum_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(self.dev_sum_file, 'w', encoding='utf-8') as f:
                f.write(str(total))
            
            # 更新内存中的值
            self._client_total = total
            logger.info(f"设置客户端总数: {total}")
            return True
        except Exception as e:
            logger.error(f"设置客户端总数失败: {e}")
            return False
    
    def reload_client_total(self) -> int:
        """重新加载客户端总数"""
        self._client_total = self._load_client_total()
        return self._client_total


# 创建全局实例
device_config = DeviceConfig()


def get_device_config() -> DeviceConfig:
    """获取设备配置实例"""
    return device_config


def get_client_total() -> int:
    """获取客户端总数"""
    return device_config.get_client_total()


def set_client_total(total: int) -> bool:
    """设置客户端总数"""
    return device_config.set_client_total(total)


def reload_client_total() -> int:
    """重新加载客户端总数"""
    return device_config.reload_client_total()


if __name__ == "__main__":
    # 测试代码
    print(f"当前客户端总数: {get_client_total()}")
    print(f"设置客户端总数为2: {set_client_total(2)}")
    print(f"重新加载后的客户端总数: {reload_client_total()}")
