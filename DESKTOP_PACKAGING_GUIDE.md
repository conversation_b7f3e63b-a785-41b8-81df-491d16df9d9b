# 考勤系统桌面程序打包指南

## 概述

本指南详细说明如何将考勤系统桌面程序打包成可在Windows 10系统上独立运行的可执行文件(.exe)。

## 打包文件结构

```
项目根目录/
├── desktop_launcher.py          # 桌面程序启动器
├── desktop_package.spec         # PyInstaller配置文件
├── build_desktop_app.py         # 打包脚本
├── build_desktop.bat           # Windows批处理脚本
├── test_desktop_package.py     # 测试脚本
├── runtime_hooks/
│   └── pyi_rth_desktop.py      # 运行时钩子
├── hooks/                      # PyInstaller钩子目录
└── dist/                       # 构建输出目录
    ├── AttendanceDesktop.exe   # 可执行文件
    └── AttendanceDesktop_Package/  # 分发包
```

## 系统要求

### 开发环境
- **Python**: 3.8+ (推荐3.11)
- **操作系统**: Windows 10/11
- **内存**: 4GB+ (构建过程消耗较大)
- **磁盘空间**: 1GB+ 可用空间

### 运行环境
- **操作系统**: Windows 10/11
- **内存**: 2GB+
- **磁盘空间**: 200MB+

## 依赖要求

### 核心依赖
```
PyQt6>=6.6.0          # GUI框架
pandas>=2.0.0          # 数据处理
openpyxl>=3.1.0        # Excel文件处理
sqlalchemy>=2.0.0      # 数据库ORM
pyinstaller>=6.0.0     # 打包工具
```

## 打包步骤

### 方法一：使用批处理脚本（推荐）

1. **运行批处理脚本**
   ```cmd
   build_desktop.bat
   ```

2. **等待构建完成**
   - 脚本会自动检查环境
   - 安装必要的依赖
   - 执行打包过程

### 方法二：使用Python脚本

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   pip install pyinstaller>=6.0.0
   ```

2. **运行打包脚本**
   ```bash
   python build_desktop_app.py
   ```

3. **可选参数**
   ```bash
   # 不清理构建目录
   python build_desktop_app.py --no-clean
   
   # 不测试可执行文件
   python build_desktop_app.py --no-test
   ```

### 方法三：手动打包

1. **准备环境**
   ```bash
   pip install pyinstaller>=6.0.0
   ```

2. **执行PyInstaller**
   ```bash
   pyinstaller --clean --noconfirm desktop_package.spec
   ```

## 打包配置说明

### PyInstaller配置 (desktop_package.spec)

**主要配置项**：
- **入口文件**: `desktop_launcher.py`
- **打包模式**: 单文件模式 (onefile)
- **控制台**: 显示控制台窗口（便于调试）
- **压缩**: 启用UPX压缩
- **隐藏导入**: PyQt6, pandas, openpyxl等
- **数据文件**: 配置文件、翻译文件等
- **运行时钩子**: 环境初始化脚本

### 运行时钩子 (pyi_rth_desktop.py)

**功能**：
- 设置应用程序路径环境变量
- 创建必要的工作目录
- 配置中文语言环境
- 设置Qt高DPI支持

## 测试验证

### 自动测试

```bash
# 测试默认路径的可执行文件
python test_desktop_package.py

# 测试指定路径的可执行文件
python test_desktop_package.py "dist/AttendanceDesktop.exe"
```

### 手动测试

1. **启动测试**
   - 双击 `AttendanceDesktop.exe`
   - 检查程序是否正常启动
   - 观察控制台输出

2. **功能测试**
   - 检查主界面是否正常显示
   - 测试菜单和按钮功能
   - 验证数据加载和显示

3. **目录测试**
   - 检查是否创建了工作目录
   - 验证配置文件是否正确加载

## 分发说明

### 分发包内容

```
AttendanceDesktop_Package/
├── AttendanceDesktop.exe    # 主程序
└── 使用说明.txt            # 用户说明
```

### 用户使用方法

1. **解压分发包**到目标目录
2. **双击**`AttendanceDesktop.exe`启动程序
3. **首次运行**会自动创建工作目录：
   - `temp/`: 临时文件目录
   - `temp_bak/`: 数据备份目录
   - `temp_cfg/`: 配置文件目录
   - `logs/`: 日志文件目录

## 故障排除

### 常见问题

#### 1. 程序无法启动
**症状**: 双击exe文件无反应或闪退
**解决方案**:
- 检查是否有杀毒软件阻止
- 确保系统满足最低要求
- 查看控制台输出错误信息

#### 2. 中文显示异常
**症状**: 界面中文显示为乱码
**解决方案**:
- 检查系统区域设置
- 确保系统支持UTF-8编码
- 重新启动程序

#### 3. 文件权限错误
**症状**: 程序提示文件权限不足
**解决方案**:
- 以管理员身份运行程序
- 检查程序目录的写入权限
- 将程序移到用户目录下

#### 4. 依赖库缺失
**症状**: 程序启动时提示缺少DLL文件
**解决方案**:
- 重新打包程序
- 检查PyInstaller配置
- 安装Visual C++ Redistributable

### 调试方法

#### 启用详细日志
程序运行时会在控制台显示详细的日志信息，包括：
- 环境初始化过程
- 模块加载状态
- 错误和警告信息

#### 查看日志文件
程序会在`logs/`目录下生成日志文件，可以查看详细的运行日志。

## 技术特性

### 路径管理
- 自动检测打包环境和开发环境
- 使用相对路径，避免硬编码
- 运行时动态创建工作目录

### 依赖处理
- 自动包含所有必要的Python模块
- 处理PyQt6的复杂依赖关系
- 优化打包大小，排除不必要的模块

### 稳定性保证
- 完善的异常处理机制
- 资源清理和内存管理
- 多线程安全处理

## 维护说明

### 更新打包配置
当项目结构或依赖发生变化时，需要更新：
1. `desktop_package.spec` - 更新隐藏导入和数据文件
2. `runtime_hooks/pyi_rth_desktop.py` - 更新环境设置
3. `build_desktop_app.py` - 更新构建逻辑

### 版本管理
建议为每个版本创建独立的分发包，包含版本信息和更新日志。

## 总结

本打包方案提供了完整的桌面程序打包解决方案，具有以下特点：

✅ **易于使用**: 提供批处理脚本，一键打包
✅ **功能完整**: 保持所有原有功能
✅ **稳定可靠**: 经过充分测试验证
✅ **用户友好**: 包含详细的使用说明
✅ **维护简单**: 配置清晰，易于更新

通过本方案打包的程序可以在Windows 10系统上独立运行，无需安装Python环境，大大简化了部署和使用过程。
