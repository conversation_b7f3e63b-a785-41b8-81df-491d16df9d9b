#!/usr/bin/env python3
"""
考勤系统路径管理模块
统一处理开发环境和打包环境的路径问题
"""

import os
import sys
from pathlib import Path
from typing import Optional


class PathManager:
    """路径管理器，统一处理开发环境和打包环境的路径"""
    
    _instance: Optional['PathManager'] = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_paths()
            PathManager._initialized = True
    
    def _setup_paths(self):
        """设置路径"""
        self.is_frozen = getattr(sys, 'frozen', False)
        
        if self.is_frozen:
            # 打包环境：使用可执行文件所在目录
            self._setup_frozen_paths()
        else:
            # 开发环境：使用项目根目录
            self._setup_development_paths()
    
    def _setup_frozen_paths(self):
        """设置打包环境的路径"""
        # 优先使用环境变量（由运行时钩子设置）
        if 'ATTENDANCE_APP_PATH' in os.environ:
            self.app_root = Path(os.environ['ATTENDANCE_APP_PATH'])
            self.temp_dir = Path(os.environ['ATTENDANCE_TEMP_DIR'])
            self.temp_bak_dir = Path(os.environ['ATTENDANCE_TEMP_BAK_DIR'])
            self.temp_cfg_dir = Path(os.environ['ATTENDANCE_TEMP_CFG_DIR'])
            self.logs_dir = Path(os.environ['ATTENDANCE_LOGS_DIR'])
            print(f"[路径管理器] 使用环境变量设置路径")
        else:
            # 备用方案：直接计算
            self.app_root = Path(sys.executable).parent
            self.temp_dir = self.app_root / 'temp'
            self.temp_bak_dir = self.app_root / 'temp_bak'
            self.temp_cfg_dir = self.app_root / 'temp_cfg'
            self.logs_dir = self.app_root / 'logs'
            print(f"[路径管理器] 使用备用方案设置路径")

        print(f"[路径管理器] 打包环境路径配置:")
        print(f"  应用根目录: {self.app_root}")
        print(f"  临时目录: {self.temp_dir}")
        print(f"  备份目录: {self.temp_bak_dir}")
        print(f"  配置目录: {self.temp_cfg_dir}")
        print(f"  日志目录: {self.logs_dir}")

        # 确保目录存在
        self._ensure_directories_exist()
    
    def _setup_development_paths(self):
        """设置开发环境的路径"""
        # 在开发环境中，使用项目根目录
        self.app_root = Path(__file__).parent.parent
        self.temp_dir = self.app_root / 'temp'
        self.temp_bak_dir = self.app_root / 'temp_bak'
        self.temp_cfg_dir = self.app_root / 'temp_cfg'
        self.logs_dir = self.app_root / 'logs'
        
        # 确保目录存在
        self._ensure_directories_exist()
    
    def _ensure_directories_exist(self):
        """确保必要的目录存在"""
        directories = [
            self.temp_dir,
            self.temp_bak_dir,
            self.temp_cfg_dir,
            self.logs_dir
        ]
        
        for directory in directories:
            try:
                directory.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                print(f"警告: 无法创建目录 {directory}: {e}")
    
    def get_app_root(self) -> Path:
        """获取应用程序根目录"""
        return self.app_root
    
    def get_temp_dir(self) -> Path:
        """获取temp目录"""
        return self.temp_dir
    
    def get_temp_bak_dir(self) -> Path:
        """获取temp_bak目录"""
        return self.temp_bak_dir
    
    def get_temp_cfg_dir(self) -> Path:
        """获取temp_cfg目录"""
        return self.temp_cfg_dir
    
    def get_logs_dir(self) -> Path:
        """获取logs目录"""
        return self.logs_dir
    
    def get_server_logs_dir(self) -> Path:
        """获取服务端日志目录"""
        if self.is_frozen:
            # 打包环境：服务端日志也放在主日志目录中
            return self.logs_dir
        else:
            # 开发环境：使用attendance_server目录下的logs
            return self.app_root / 'attendance_server' / 'logs'
    
    def get_database_path(self, device_id: str) -> Path:
        """获取设备数据库文件路径"""
        return self.temp_dir / f"{device_id}.db"
    
    def get_backup_database_path(self, device_id: str) -> Path:
        """获取备份数据库文件路径"""
        return self.temp_bak_dir / f"{device_id}.db"
    
    def get_config_file_path(self, filename: str) -> Path:
        """获取配置文件路径"""
        return self.temp_cfg_dir / filename
    
    def get_log_file_path(self, filename: str) -> Path:
        """获取日志文件路径"""
        return self.logs_dir / filename
    
    def get_server_log_file_path(self, filename: str) -> Path:
        """获取服务端日志文件路径"""
        server_logs_dir = self.get_server_logs_dir()
        server_logs_dir.mkdir(parents=True, exist_ok=True)
        return server_logs_dir / filename

    def get_uploads_dir(self) -> Path:
        """获取uploads目录"""
        uploads_dir = self.app_root / 'uploads'
        uploads_dir.mkdir(parents=True, exist_ok=True)
        return uploads_dir

    def get_upload_file_path(self, filename: str) -> Path:
        """获取上传文件路径"""
        return self.get_uploads_dir() / filename
    
    def list_temp_files(self, pattern: str = "*") -> list[Path]:
        """列出temp目录中的文件"""
        if not self.temp_dir.exists():
            return []
        return list(self.temp_dir.glob(pattern))
    
    def list_temp_bak_files(self, pattern: str = "*") -> list[Path]:
        """列出temp_bak目录中的文件"""
        if not self.temp_bak_dir.exists():
            return []
        return list(self.temp_bak_dir.glob(pattern))
    
    def is_temp_dir_empty(self) -> bool:
        """检查temp目录是否为空"""
        return len(self.list_temp_files()) == 0
    
    def is_temp_bak_dir_empty(self) -> bool:
        """检查temp_bak目录是否为空"""
        return len(self.list_temp_bak_files()) == 0
    
    def __str__(self) -> str:
        """返回路径信息的字符串表示"""
        return f"""PathManager:
  Environment: {'Frozen' if self.is_frozen else 'Development'}
  App Root: {self.app_root}
  Temp Dir: {self.temp_dir}
  Temp Bak Dir: {self.temp_bak_dir}
  Temp Cfg Dir: {self.temp_cfg_dir}
  Logs Dir: {self.logs_dir}"""


# 创建全局实例
path_manager = PathManager()


def get_path_manager() -> PathManager:
    """获取路径管理器实例"""
    return path_manager


# 便捷函数
def get_app_root() -> Path:
    """获取应用程序根目录"""
    return path_manager.get_app_root()


def get_temp_dir() -> Path:
    """获取temp目录"""
    return path_manager.get_temp_dir()


def get_temp_bak_dir() -> Path:
    """获取temp_bak目录"""
    return path_manager.get_temp_bak_dir()


def get_temp_cfg_dir() -> Path:
    """获取temp_cfg目录"""
    return path_manager.get_temp_cfg_dir()


def get_logs_dir() -> Path:
    """获取logs目录"""
    return path_manager.get_logs_dir()


def get_database_path(device_id: str) -> Path:
    """获取设备数据库文件路径"""
    return path_manager.get_database_path(device_id)


def get_backup_database_path(device_id: str) -> Path:
    """获取备份数据库文件路径"""
    return path_manager.get_backup_database_path(device_id)


def list_temp_files(pattern: str = "*") -> list[Path]:
    """列出temp目录中的文件"""
    return path_manager.list_temp_files(pattern)


def list_temp_bak_files(pattern: str = "*") -> list[Path]:
    """列出temp_bak目录中的文件"""
    return path_manager.list_temp_bak_files(pattern)


if __name__ == "__main__":
    # 测试代码
    print(path_manager)
    print(f"\nTemp files: {list_temp_files()}")
    print(f"Temp bak files: {list_temp_bak_files()}")
