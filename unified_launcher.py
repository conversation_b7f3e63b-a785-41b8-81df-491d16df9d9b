#!/usr/bin/env python3
"""
考勤系统统一启动器（专门用于打包）
在单个进程中同时启动桌面程序和服务端程序
"""

import sys
import os
import time
import threading
import asyncio
import logging
from pathlib import Path
from typing import Optional

def get_project_root():
    """获取项目根目录"""
    if getattr(sys, 'frozen', False):
        # 打包环境：使用可执行文件所在目录
        return Path(sys.executable).parent
    else:
        # 开发环境：使用脚本文件所在目录
        return Path(__file__).parent

# 设置项目根目录并添加到Python路径
project_root = get_project_root()
sys.path.insert(0, str(project_root))

class UnifiedLauncher:
    """统一启动器"""
    
    def __init__(self):
        self.server_thread: Optional[threading.Thread] = None
        self.desktop_app = None
        self.is_shutting_down = False
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('UnifiedLauncher')
        logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        
        if not logger.handlers:
            logger.addHandler(console_handler)
        return logger

    def _setup_high_dpi_support(self):
        """设置高DPI支持（兼容PyQt6）"""
        try:
            from PyQt6.QtCore import Qt

            # 在PyQt6中，Qt6默认启用高DPI缩放，AA_EnableHighDpiScaling已被移除
            # 我们只需要设置仍然存在的属性

            # 尝试设置高DPI像素图支持（如果存在）
            try:
                if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
                    self.desktop_app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
                    self.logger.info("已启用高DPI像素图支持")
            except AttributeError:
                self.logger.info("AA_UseHighDpiPixmaps属性不存在，跳过设置")

            # 尝试设置DPI缩放策略（如果存在）
            try:
                if hasattr(Qt.ApplicationAttribute, 'AA_DisableHighDpiScaling'):
                    # 确保不禁用高DPI缩放
                    self.desktop_app.setAttribute(Qt.ApplicationAttribute.AA_DisableHighDpiScaling, False)
                    self.logger.info("已确保高DPI缩放未被禁用")
            except AttributeError:
                self.logger.info("AA_DisableHighDpiScaling属性不存在，跳过设置")

            # 设置缩放因子舍入策略（如果存在）
            try:
                if hasattr(Qt, 'HighDpiScaleFactorRoundingPolicy'):
                    if hasattr(Qt.HighDpiScaleFactorRoundingPolicy, 'PassThrough'):
                        self.desktop_app.setHighDpiScaleFactorRoundingPolicy(
                            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
                        )
                        self.logger.info("已设置高DPI缩放因子舍入策略")
            except AttributeError:
                self.logger.info("高DPI缩放因子舍入策略不可用，跳过设置")

            self.logger.info("高DPI支持设置完成")

        except Exception as e:
            self.logger.warning(f"设置高DPI支持时发生错误: {e}")
            self.logger.info("继续使用默认DPI设置")
    
    def setup_environment(self):
        """设置运行环境"""
        try:
            # 设置中文编码
            if sys.platform.startswith('win'):
                import locale
                try:
                    locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
                except:
                    try:
                        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
                    except:
                        pass  # 如果设置失败，使用默认编码
            
            # 设置环境变量
            os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
            
            self.logger.info("环境设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"环境设置失败: {e}")
            return False
    
    def start_server_thread(self, host: str = "0.0.0.0", port: int = 8000) -> bool:
        """在线程中启动服务端"""
        self.logger.info(f"启动服务端线程: {host}:{port}")

        def server_runner():
            """服务端运行函数"""
            try:
                # 设置环境变量
                os.environ['HOST'] = host
                os.environ['PORT'] = str(port)
                self.logger.info("服务端环境变量设置完成")

                # 导入并启动服务端
                try:
                    import uvicorn
                    from attendance_server.main import app
                    self.logger.info("服务端模块导入成功")
                except ImportError as e:
                    self.logger.error(f"导入服务端模块失败: {e}")
                    return

                # 配置uvicorn
                try:
                    config = uvicorn.Config(
                        app=app,
                        host=host,
                        port=port,
                        log_level="info",
                        access_log=True
                    )
                    server = uvicorn.Server(config)
                    self.logger.info("uvicorn服务器配置完成")
                except Exception as e:
                    self.logger.error(f"配置uvicorn服务器失败: {e}")
                    return

                # 在新的事件循环中运行服务器
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    self.logger.info(f"服务端开始监听 {host}:{port}")
                    loop.run_until_complete(server.serve())
                except Exception as e:
                    self.logger.error(f"运行服务端事件循环失败: {e}")
                    import traceback
                    traceback.print_exc()

            except Exception as e:
                self.logger.error(f"服务端线程运行错误: {e}")
                import traceback
                traceback.print_exc()

        try:
            # 启动服务端线程
            self.server_thread = threading.Thread(
                target=server_runner,
                daemon=True,
                name="ServerThread"
            )
            self.server_thread.start()
            self.logger.info("服务端线程启动成功")

            # 等待服务端启动
            self.logger.info("等待服务端启动...")
            time.sleep(5)  # 给服务端更多启动时间

            return True

        except Exception as e:
            self.logger.error(f"启动服务端线程失败: {e}")
            return False
    
    def start_desktop_app(self) -> bool:
        """启动桌面程序（在主线程中）"""
        try:
            self.logger.info("启动桌面程序...")

            # 导入PyQt6和桌面程序
            try:
                from PyQt6.QtWidgets import QApplication
                from PyQt6.QtCore import Qt
                from desktop.main_window import MainWindow
                self.logger.info("PyQt6模块导入成功")
            except ImportError as e:
                self.logger.error(f"导入PyQt6模块失败: {e}")
                return False

            # 创建QApplication实例
            try:
                self.desktop_app = QApplication(sys.argv)
                self.logger.info("QApplication实例创建成功")
            except Exception as e:
                self.logger.error(f"创建QApplication实例失败: {e}")
                return False

            # 设置应用程序属性
            try:
                self.desktop_app.setApplicationName("考勤系统")
                self.desktop_app.setApplicationVersion("1.0.0")
                self.desktop_app.setOrganizationName("考勤系统开发团队")
                self.logger.info("应用程序属性设置成功")
            except Exception as e:
                self.logger.warning(f"设置应用程序属性失败: {e}")
                # 继续执行，这不是致命错误

            # 设置高DPI支持（兼容PyQt6）
            self._setup_high_dpi_support()

            # 创建主窗口
            try:
                window = MainWindow()
                self.logger.info("主窗口创建成功")
            except Exception as e:
                self.logger.error(f"创建主窗口失败: {e}")
                import traceback
                traceback.print_exc()
                return False

            # 显示窗口
            try:
                window.show()
                self.logger.info("主窗口显示成功")
            except Exception as e:
                self.logger.error(f"显示主窗口失败: {e}")
                return False

            self.logger.info("桌面程序启动完成，开始运行事件循环")

            # 运行事件循环
            try:
                exit_code = self.desktop_app.exec()
                self.logger.info(f"桌面程序已退出，退出代码: {exit_code}")
                return exit_code == 0
            except Exception as e:
                self.logger.error(f"运行事件循环时发生错误: {e}")
                return False

        except Exception as e:
            self.logger.error(f"启动桌面程序时发生未预期的错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def start_desktop_app_fast(self) -> bool:
        """快速启动桌面程序（优化版本）"""
        try:
            self.logger.info("快速启动桌面程序...")

            # 导入PyQt6和桌面程序
            try:
                from PyQt6.QtWidgets import QApplication
                from PyQt6.QtCore import Qt, QTimer
                from desktop.main_window import MainWindow
                self.logger.info("PyQt6模块导入成功")
            except ImportError as e:
                self.logger.error(f"导入PyQt6模块失败: {e}")
                return False

            # 创建QApplication实例
            try:
                self.desktop_app = QApplication(sys.argv)
                self.logger.info("QApplication实例创建成功")
            except Exception as e:
                self.logger.error(f"创建QApplication实例失败: {e}")
                return False

            # 设置应用程序属性
            try:
                self.desktop_app.setApplicationName("考勤系统")
                self.desktop_app.setApplicationVersion("1.0.0")
                self.desktop_app.setOrganizationName("考勤系统开发团队")
                self.logger.info("应用程序属性设置成功")
            except Exception as e:
                self.logger.warning(f"设置应用程序属性失败: {e}")

            # 设置高DPI支持
            self._setup_high_dpi_support()

            # 创建主窗口
            try:
                self.main_window = MainWindow()
                self.logger.info("主窗口创建成功")
            except Exception as e:
                self.logger.error(f"创建主窗口失败: {e}")
                import traceback
                traceback.print_exc()
                return False

            # 立即显示窗口（快速响应用户）
            try:
                self.main_window.show()
                self.logger.info("主窗口显示成功")
            except Exception as e:
                self.logger.error(f"显示主窗口失败: {e}")
                return False

            # 设置后台服务启动定时器（延迟启动服务端）
            self.setup_background_services()

            self.logger.info("桌面程序快速启动完成，开始运行事件循环")

            # 运行事件循环
            try:
                exit_code = self.desktop_app.exec()
                self.logger.info(f"桌面程序已退出，退出代码: {exit_code}")
                return exit_code == 0
            except Exception as e:
                self.logger.error(f"运行事件循环时发生错误: {e}")
                return False

        except Exception as e:
            self.logger.error(f"快速启动桌面程序时发生未预期的错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def setup_background_services(self):
        """设置后台服务启动"""
        try:
            from PyQt6.QtCore import QTimer

            # 创建定时器，延迟启动后台服务
            self.service_timer = QTimer()
            self.service_timer.setSingleShot(True)  # 只执行一次
            self.service_timer.timeout.connect(self.start_background_services)

            # 延迟2秒启动后台服务，让界面先显示
            self.service_timer.start(2000)
            self.logger.info("后台服务启动定时器已设置（2秒后启动）")

        except Exception as e:
            self.logger.error(f"设置后台服务启动失败: {e}")

    def start_background_services(self):
        """启动后台服务"""
        try:
            self.logger.info("开始启动后台服务...")

            # 更新主窗口状态
            if hasattr(self, 'main_window') and self.main_window:
                try:
                    # 通知主窗口后台服务正在启动
                    self.main_window.update_service_status("正在启动后台服务...")
                except Exception as e:
                    self.logger.warning(f"更新主窗口状态失败: {e}")

            # 启动服务端线程
            if self.start_server_thread():
                self.logger.info("后台HTTP服务启动成功")
                if hasattr(self, 'main_window') and self.main_window:
                    try:
                        self.main_window.update_service_status("后台服务启动完成")
                    except Exception as e:
                        self.logger.warning(f"更新服务状态失败: {e}")
            else:
                self.logger.error("后台HTTP服务启动失败")
                if hasattr(self, 'main_window') and self.main_window:
                    try:
                        self.main_window.update_service_status("后台服务启动失败")
                    except Exception as e:
                        self.logger.warning(f"更新服务状态失败: {e}")

        except Exception as e:
            self.logger.error(f"启动后台服务时发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    def check_dependencies(self) -> bool:
        """检查必要的依赖"""
        try:
            required_modules = ['PyQt6', 'fastapi', 'uvicorn', 'pandas', 'sqlite3']
            missing_modules = []
            
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)
            
            if missing_modules:
                self.logger.error(f"缺少必要的模块: {', '.join(missing_modules)}")
                return False
            
            self.logger.info("依赖检查通过")
            return True
            
        except Exception as e:
            self.logger.error(f"依赖检查失败: {e}")
            return False
    
    def shutdown(self):
        """关闭所有服务"""
        if self.is_shutting_down:
            return
            
        self.is_shutting_down = True
        self.logger.info("开始关闭所有服务...")
        
        # 关闭桌面程序
        if self.desktop_app:
            try:
                self.desktop_app.quit()
            except Exception as e:
                self.logger.error(f"关闭桌面程序失败: {e}")
        
        # 服务端线程是daemon线程，会自动关闭
        if self.server_thread and self.server_thread.is_alive():
            self.logger.info("服务端线程将随主程序退出")
        
        self.logger.info("所有服务已关闭")
    
    def run(self) -> int:
        """运行统一启动器（优化启动顺序）"""
        try:
            self.logger.info("考勤系统统一启动器启动中...")

            # 1. 设置环境
            if not self.setup_environment():
                self.logger.error("环境设置失败")
                return 1

            # 2. 检查依赖
            if not self.check_dependencies():
                self.logger.error("依赖检查失败")
                return 1

            # 3. 快速启动桌面程序（优先显示界面）
            success = self.start_desktop_app_fast()

            return 0 if success else 1

        except KeyboardInterrupt:
            self.logger.info("用户中断，程序退出")
            return 0

        except Exception as e:
            self.logger.error(f"程序运行时发生未预期的错误: {e}")
            import traceback
            traceback.print_exc()
            return 1

        finally:
            self.shutdown()

def main():
    """主函数"""
    launcher = UnifiedLauncher()
    exit_code = launcher.run()
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
