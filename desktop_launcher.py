#!/usr/bin/env python3
"""
考勤系统桌面程序启动器
专门用于PyInstaller打包的入口文件
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """设置运行环境"""
    # 获取项目根目录
    if getattr(sys, 'frozen', False):
        # 打包环境：使用可执行文件所在目录
        project_root = Path(sys.executable).parent
    else:
        # 开发环境：使用脚本文件所在目录
        project_root = Path(__file__).parent
    
    # 添加项目根目录到Python路径
    sys.path.insert(0, str(project_root))
    
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # 设置中文编码支持
    if sys.platform.startswith('win'):
        try:
            import locale
            locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
        except:
            pass
    
    return project_root

def main():
    """主函数"""
    try:
        print("考勤系统桌面程序启动中...")
        
        # 设置环境
        project_root = setup_environment()
        print(f"项目根目录: {project_root}")
        
        # 导入并启动桌面程序
        from desktop.main_window import main as desktop_main
        
        print("正在启动桌面程序...")
        return desktop_main()
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保所有必要的模块都已正确安装")
        input("按任意键退出...")
        return 1
        
    except Exception as e:
        print(f"启动桌面程序时发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("按任意键退出...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
