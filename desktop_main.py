#!/usr/bin/env python3
"""
考勤系统桌面程序独立启动入口
专门用于打包成独立的桌面应用程序
"""

import sys
import os
from pathlib import Path

def get_project_root():
    """获取项目根目录"""
    if getattr(sys, 'frozen', False):
        # 打包环境：使用可执行文件所在目录
        return Path(sys.executable).parent
    else:
        # 开发环境：使用脚本文件所在目录
        return Path(__file__).parent

# 设置项目根目录并添加到Python路径
project_root = get_project_root()
sys.path.insert(0, str(project_root))

def setup_environment():
    """设置运行环境"""
    try:
        # 设置中文编码
        if sys.platform.startswith('win'):
            import locale
            try:
                locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
            except:
                try:
                    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
                except:
                    pass  # 如果设置失败，使用默认编码
        
        # 设置环境变量
        os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
        
        print("环境设置完成")
        return True
        
    except Exception as e:
        print(f"环境设置失败: {e}")
        return False

def check_dependencies():
    """检查必要的依赖"""
    try:
        required_modules = ['PyQt6', 'pandas', 'sqlite3']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            print(f"错误: 缺少必要的模块: {', '.join(missing_modules)}")
            return False
        
        print("依赖检查通过")
        return True
        
    except Exception as e:
        print(f"依赖检查失败: {e}")
        return False

def main():
    """主函数"""
    try:
        print("考勤系统桌面程序启动中...")
        
        # 1. 设置环境
        if not setup_environment():
            print("环境设置失败，程序退出")
            sys.exit(1)
        
        # 2. 检查依赖
        if not check_dependencies():
            print("依赖检查失败，程序退出")
            sys.exit(1)
        
        # 3. 导入并启动桌面程序
        try:
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtCore import Qt
            from desktop.main_window import MainWindow
            
            # 创建QApplication实例
            app = QApplication(sys.argv)
            
            # 设置应用程序属性
            app.setApplicationName("考勤系统")
            app.setApplicationVersion("1.0.0")
            app.setOrganizationName("考勤系统开发团队")
            
            # 设置高DPI支持
            app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
            app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
            
            print("正在创建主窗口...")
            
            # 创建主窗口
            window = MainWindow()
            
            # 显示窗口
            window.show()
            
            print("桌面程序启动完成")
            
            # 运行事件循环
            exit_code = app.exec()
            
            print("桌面程序已退出")
            return exit_code
            
        except ImportError as e:
            print(f"导入模块失败: {e}")
            print("请确保所有必要的模块都已正确安装")
            return 1
            
        except Exception as e:
            print(f"启动桌面程序时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return 1
    
    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
        return 0
        
    except Exception as e:
        print(f"程序运行时发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
