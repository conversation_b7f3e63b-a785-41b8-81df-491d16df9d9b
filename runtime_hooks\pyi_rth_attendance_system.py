"""
考勤系统运行时钩子
在程序启动时设置必要的环境变量和路径
"""

import os
import sys
from pathlib import Path

# 获取可执行文件所在目录
if getattr(sys, 'frozen', False):
    # 如果是打包后的可执行文件
    application_path = Path(sys.executable).parent
    # 设置临时目录为可执行文件所在目录
    temp_dir = application_path / 'temp'
    temp_bak_dir = application_path / 'temp_bak'
    temp_cfg_dir = application_path / 'temp_cfg'
    logs_dir = application_path / 'logs'

    # 创建必要的目录
    for directory in [temp_dir, temp_bak_dir, temp_cfg_dir, logs_dir]:
        directory.mkdir(parents=True, exist_ok=True)

    # 设置环境变量，供其他模块使用
    os.environ['ATTENDANCE_APP_PATH'] = str(application_path)
    os.environ['ATTENDANCE_TEMP_DIR'] = str(temp_dir)
    os.environ['ATTENDANCE_TEMP_BAK_DIR'] = str(temp_bak_dir)
    os.environ['ATTENDANCE_TEMP_CFG_DIR'] = str(temp_cfg_dir)
    os.environ['ATTENDANCE_LOGS_DIR'] = str(logs_dir)

    # 设置环境变量
    os.environ['ATTENDANCE_SYSTEM_ROOT'] = str(application_path)
    os.environ['ATTENDANCE_SYSTEM_TEMP'] = str(temp_dir)
    os.environ['ATTENDANCE_SYSTEM_TEMP_BAK'] = str(temp_bak_dir)
    os.environ['ATTENDANCE_SYSTEM_TEMP_CFG'] = str(temp_cfg_dir)
    os.environ['ATTENDANCE_SYSTEM_LOGS'] = str(logs_dir)

    # 设置语言环境
    os.environ['LANG'] = 'zh_CN.UTF-8'
    os.environ['LC_ALL'] = 'zh_CN.UTF-8'
    os.environ['TZ'] = 'Asia/Shanghai'

    # 输出路径信息（仅在调试时）
    print("考勤系统运行时环境初始化完成")
    print(f"应用程序路径: {application_path}")
    print(f"临时目录: {temp_dir}")
    print(f"备份目录: {temp_bak_dir}")
    print(f"配置目录: {temp_cfg_dir}")
    print(f"日志目录: {logs_dir}")

    # 验证环境变量设置
    print(f"环境变量验证:")
    print(f"  ATTENDANCE_APP_PATH: {os.environ.get('ATTENDANCE_APP_PATH', 'NOT SET')}")
    print(f"  ATTENDANCE_TEMP_DIR: {os.environ.get('ATTENDANCE_TEMP_DIR', 'NOT SET')}")
    print(f"  ATTENDANCE_LOGS_DIR: {os.environ.get('ATTENDANCE_LOGS_DIR', 'NOT SET')}")
else:
    # 开发环境，不做特殊处理
    pass
