#!/usr/bin/env python3
"""
考勤系统桌面程序打包脚本
专门用于将桌面程序打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path
import argparse

class DesktopAppBuilder:
    """桌面程序构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "desktop_app.spec"
        
    def log(self, message: str, level: str = "INFO"):
        """打印日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def run_command(self, cmd: list, cwd: Path = None, check: bool = True) -> subprocess.CompletedProcess:
        """运行命令并返回结果"""
        if cwd is None:
            cwd = self.project_root
            
        self.log(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                check=check,
                encoding='utf-8',
                errors='replace'
            )
            
            if result.stdout:
                self.log(f"输出: {result.stdout.strip()}")
            if result.stderr:
                self.log(f"错误: {result.stderr.strip()}", "WARNING")
                
            return result
            
        except subprocess.CalledProcessError as e:
            self.log(f"命令执行失败: {e}", "ERROR")
            if e.stdout:
                self.log(f"标准输出: {e.stdout}", "ERROR")
            if e.stderr:
                self.log(f"错误输出: {e.stderr}", "ERROR")
            raise
    
    def check_python_version(self):
        """检查Python版本"""
        self.log("检查Python版本...")
        
        if sys.version_info < (3, 8):
            raise RuntimeError(f"需要Python 3.8或更高版本，当前版本: {sys.version}")
        
        self.log(f"Python版本检查通过: {sys.version}")
    
    def install_build_dependencies(self):
        """安装构建依赖"""
        self.log("安装构建依赖...")
        
        # 获取pip可执行文件路径
        python_exe = Path(sys.executable)
        pip_exe = python_exe.parent / "pip.exe" if sys.platform.startswith('win') else python_exe.parent / "pip"
        
        if not pip_exe.exists():
            pip_exe = python_exe.parent / "Scripts" / "pip.exe" if sys.platform.startswith('win') else python_exe.parent / "bin" / "pip"
        
        if not pip_exe.exists():
            # 使用python -m pip
            pip_exe = python_exe
            pip_cmd = [str(pip_exe), "-m", "pip"]
        else:
            pip_cmd = [str(pip_exe)]
        
        # 升级pip
        self.run_command(pip_cmd + ["install", "--upgrade", "pip"])
        
        # 安装PyInstaller
        self.run_command(pip_cmd + ["install", "pyinstaller>=6.0.0"])
        
        # 检查requirements.txt并安装依赖
        requirements_file = self.project_root / "requirements.txt"
        if requirements_file.exists():
            self.run_command(pip_cmd + ["install", "-r", str(requirements_file)])
        else:
            # 安装基本依赖
            basic_deps = [
                "PyQt6>=6.6.0",
                "pandas>=2.0.0",
                "openpyxl>=3.1.0",
                "sqlalchemy>=2.0.0"
            ]
            for dep in basic_deps:
                self.run_command(pip_cmd + ["install", dep])
    
    def clean_build_directories(self):
        """清理构建目录"""
        self.log("清理构建目录...")
        
        for directory in [self.dist_dir, self.build_dir]:
            if directory.exists():
                self.log(f"删除目录: {directory}")
                shutil.rmtree(directory)
        
        # 清理PyInstaller缓存
        pycache_dirs = list(self.project_root.rglob("__pycache__"))
        for cache_dir in pycache_dirs:
            if cache_dir.is_dir():
                shutil.rmtree(cache_dir)
        
        self.log("构建目录清理完成")
    
    def create_necessary_directories(self):
        """创建必要的目录"""
        self.log("创建必要的目录...")
        
        directories = [
            self.project_root / "temp",
            self.project_root / "temp_bak", 
            self.project_root / "temp_cfg",
            self.project_root / "logs",
            self.project_root / "runtime_hooks"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        self.log("必要目录创建完成")
    
    def build_executable(self):
        """构建可执行文件"""
        self.log("开始构建桌面程序可执行文件...")
        
        if not self.spec_file.exists():
            raise FileNotFoundError(f"规格文件不存在: {self.spec_file}")
        
        # 使用PyInstaller构建
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",  # 清理临时文件
            "--noconfirm",  # 不询问确认
            str(self.spec_file)
        ]
        
        self.run_command(cmd)
        
        # 检查构建结果
        exe_file = self.dist_dir / "AttendanceDesktop.exe"
        if exe_file.exists():
            file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
            self.log(f"构建成功! 可执行文件: {exe_file}")
            self.log(f"文件大小: {file_size:.2f} MB")
            return exe_file
        else:
            raise RuntimeError("构建失败，未找到可执行文件")
    
    def verify_executable(self, exe_file: Path):
        """验证可执行文件"""
        self.log("验证可执行文件...")
        
        if not exe_file.exists():
            raise FileNotFoundError(f"可执行文件不存在: {exe_file}")
        
        # 简单的启动测试（快速退出）
        try:
            result = self.run_command(
                [str(exe_file), "--help"],
                check=False
            )
            
            if result.returncode == 0:
                self.log("可执行文件验证通过")
            else:
                self.log("可执行文件验证失败，但文件存在", "WARNING")
                
        except Exception as e:
            self.log(f"验证可执行文件时发生错误: {e}", "WARNING")
    
    def create_distribution_package(self, exe_file: Path):
        """创建分发包"""
        self.log("创建分发包...")
        
        # 创建分发目录
        package_dir = self.dist_dir / "AttendanceDesktop_Package"
        package_dir.mkdir(exist_ok=True)
        
        # 复制可执行文件
        shutil.copy2(exe_file, package_dir / "AttendanceDesktop.exe")
        
        # 创建使用说明
        readme_content = """# 考勤系统桌面程序使用说明

## 系统要求
- Windows 10 或更高版本
- 无需安装Python环境

## 使用方法
1. 双击 AttendanceDesktop.exe 启动程序
2. 程序会自动创建必要的工作目录
3. 首次运行时会进行初始化设置

## 功能特性
- 考勤记录管理和查看
- 数据同步和备份
- Excel数据导出
- 实时日志监控

## 目录结构
程序运行后会在同级目录创建以下文件夹：
- temp/: 临时文件目录
- temp_bak/: 数据备份目录
- temp_cfg/: 配置文件目录
- logs/: 日志文件目录

## 故障排除
1. 如果程序无法启动，请检查是否有杀毒软件阻止
2. 确保程序有足够的文件系统权限
3. 如遇问题，请查看logs目录中的日志文件

## 注意事项
- 请勿删除程序同级目录下的工作文件夹
- 建议定期备份temp_bak目录中的数据
- 程序关闭时会自动保存当前状态
"""
        
        readme_file = package_dir / "使用说明.txt"
        readme_file.write_text(readme_content, encoding='utf-8')
        
        self.log(f"分发包创建完成: {package_dir}")
        return package_dir
    
    def build(self, clean: bool = True, verify: bool = True):
        """执行完整的构建流程"""
        try:
            self.log("开始构建考勤系统桌面程序")
            
            # 1. 检查环境
            self.check_python_version()
            
            # 2. 安装依赖
            self.install_build_dependencies()
            
            # 3. 清理构建目录
            if clean:
                self.clean_build_directories()
            
            # 4. 创建必要目录
            self.create_necessary_directories()
            
            # 5. 构建可执行文件
            exe_file = self.build_executable()
            
            # 6. 验证可执行文件
            if verify:
                self.verify_executable(exe_file)
            
            # 7. 创建分发包
            package_dir = self.create_distribution_package(exe_file)
            
            self.log("构建完成!")
            self.log(f"可执行文件: {exe_file}")
            self.log(f"分发包: {package_dir}")
            
            return exe_file, package_dir
            
        except Exception as e:
            self.log(f"构建失败: {e}", "ERROR")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="考勤系统桌面程序打包工具")
    parser.add_argument("--no-clean", action="store_true", help="不清理构建目录")
    parser.add_argument("--no-verify", action="store_true", help="不验证可执行文件")
    
    args = parser.parse_args()
    
    builder = DesktopAppBuilder()
    
    try:
        exe_file, package_dir = builder.build(
            clean=not args.no_clean,
            verify=not args.no_verify
        )
        
        print("\n" + "="*60)
        print("桌面程序构建成功!")
        print(f"可执行文件: {exe_file}")
        print(f"分发包目录: {package_dir}")
        print("="*60)
        
    except Exception as e:
        print(f"\n构建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
