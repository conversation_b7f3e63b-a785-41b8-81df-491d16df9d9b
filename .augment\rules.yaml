# Augment 规则配置 - 考勤系统路径管理规范
# Python 3.11.5+ 兼容性要求

name: "考勤系统路径管理规范"
version: "1.0.0"
python_version: ">=3.11.5"

rules:
  # 1. 检测 Path(__file__).parent 硬编码路径
  - id: "hardcoded-path-file-parent"
    name: "检测 Path(__file__).parent 硬编码路径"
    severity: "warning"
    pattern: |
      Path\(__file__\)\.parent(?:\.parent)*(?:\s*/\s*["\'][^"\']+["\'])?
    message: "发现硬编码路径，应使用 path_manager 统一管理路径"
    suggestion: "使用 from config.path_manager import get_path_manager; path_manager = get_path_manager()"
    files:
      include: ["**/*.py"]
      exclude: ["config/path_manager.py", "runtime_hooks/**"]

  # 2. 检测字符串硬编码目录名
  - id: "hardcoded-directory-strings"
    name: "检测硬编码目录字符串"
    severity: "warning"
    pattern: |
      (?:["'](?:temp|uploads|logs|temp_bak|temp_cfg|backups|attendance_server)["'])
    message: "发现硬编码目录名，应使用 path_manager 获取标准路径"
    suggestion: |
      使用对应的 path_manager 方法：
      - "temp" → path_manager.get_temp_dir()
      - "uploads" → path_manager.get_uploads_dir()
      - "logs" → path_manager.get_logs_dir()
      - "temp_bak" → path_manager.get_temp_bak_dir()
      - "temp_cfg" → path_manager.get_temp_cfg_dir()
    files:
      include: ["**/*.py"]
      exclude: ["config/path_manager.py", "runtime_hooks/**"]

  # 3. 检测 os.path.join() 硬编码路径
  - id: "hardcoded-os-path-join"
    name: "检测 os.path.join() 硬编码路径"
    severity: "warning"
    pattern: |
      os\.path\.join\([^)]*(?:["'](?:temp|uploads|logs|temp_bak|temp_cfg|backups)["'])[^)]*\)
    message: "发现使用 os.path.join() 构建硬编码路径，应使用 pathlib.Path 和 path_manager"
    suggestion: "使用 pathlib.Path 和 path_manager 方法替代 os.path.join()"
    files:
      include: ["**/*.py"]
      exclude: ["config/path_manager.py"]

  # 4. 检测文件操作中的硬编码路径
  - id: "hardcoded-file-operations"
    name: "检测文件操作中的硬编码路径"
    severity: "error"
    pattern: |
      (?:open|makedirs|mkdir|exists|rmdir|remove|listdir)\s*\(\s*(?:f?["'][^"']*(?:temp|uploads|logs|temp_bak|temp_cfg|backups)[^"']*["']|[^)]*(?:temp|uploads|logs|temp_bak|temp_cfg|backups)[^)]*)
    message: "文件操作中发现硬编码路径，可能导致打包后路径错误"
    suggestion: "使用 path_manager 获取正确路径，确保开发和打包环境兼容"
    files:
      include: ["**/*.py"]
      exclude: ["config/path_manager.py", "runtime_hooks/**"]

  # 5. 检测路径拼接操作符
  - id: "hardcoded-path-concatenation"
    name: "检测路径拼接操作符"
    severity: "warning"
    pattern: |
      (?:[a-zA-Z_][a-zA-Z0-9_]*\s*/\s*["'](?:temp|uploads|logs|temp_bak|temp_cfg|backups)["']|["'](?:temp|uploads|logs|temp_bak|temp_cfg|backups)["']\s*/\s*[a-zA-Z_][a-zA-Z0-9_]*)
    message: "发现使用 / 操作符拼接硬编码路径"
    suggestion: "使用 path_manager 方法获取基础路径，然后使用 pathlib.Path 进行路径操作"
    files:
      include: ["**/*.py"]
      exclude: ["config/path_manager.py"]

  # 6. 检测缺少 path_manager 导入的文件
  - id: "missing-path-manager-import"
    name: "检测缺少 path_manager 导入"
    severity: "info"
    condition: |
      file_contains_any([
        "temp", "uploads", "logs", "temp_bak", "temp_cfg", "backups"
      ]) and not file_contains("from config.path_manager import")
    message: "文件中使用了路径相关操作但未导入 path_manager"
    suggestion: "添加导入：from config.path_manager import get_path_manager"
    files:
      include: ["**/*.py"]
      exclude: ["config/path_manager.py", "runtime_hooks/**", "test_*.py"]

  # 7. 检测环境变量硬编码
  - id: "hardcoded-environment-variables"
    name: "检测环境变量硬编码使用"
    severity: "warning"
    pattern: |
      os\.environ(?:\[["']ATTENDANCE_[^"']+["']\]|\.get\(["']ATTENDANCE_[^"']+["'])
    message: "直接使用环境变量，建议通过 path_manager 统一管理"
    suggestion: "path_manager 已处理环境变量逻辑，直接使用其方法即可"
    files:
      include: ["**/*.py"]
      exclude: ["config/path_manager.py", "runtime_hooks/**"]

# 自动修复建议
auto_fix_suggestions:
  # Path(__file__).parent 替换
  - pattern: "Path\\(__file__\\)\\.parent(?:\\.parent)?"
    replacement: "path_manager.get_app_root()"
    add_import: "from config.path_manager import get_path_manager\npath_manager = get_path_manager()"

  # 常用目录字符串替换
  - pattern: '"temp"'
    replacement: "path_manager.get_temp_dir()"
  - pattern: '"uploads"'
    replacement: "path_manager.get_uploads_dir()"
  - pattern: '"logs"'
    replacement: "path_manager.get_logs_dir()"
  - pattern: '"temp_bak"'
    replacement: "path_manager.get_temp_bak_dir()"
  - pattern: '"temp_cfg"'
    replacement: "path_manager.get_temp_cfg_dir()"

# 检查配置
check_config:
  # 运行检查的时机
  on_save: true
  on_commit: true
  
  # 排除的文件模式
  exclude_patterns:
    - "**/__pycache__/**"
    - "**/dist/**"
    - "**/build/**"
    - "**/.git/**"
    - "**/venv/**"
    - "**/env/**"

  # 严重性级别过滤
  min_severity: "info"
  
  # 输出格式
  output_format: "detailed"
  show_suggestions: true
  show_context: true

# 项目特定配置
project_config:
  # 路径管理器位置
  path_manager_module: "config.path_manager"
  
  # 标准目录名称
  standard_directories:
    - "temp"
    - "temp_bak" 
    - "temp_cfg"
    - "logs"
    - "uploads"
    - "backups"
  
  # 允许硬编码路径的文件
  allowed_hardcode_files:
    - "config/path_manager.py"
    - "runtime_hooks/pyi_rth_attendance_system.py"
    - "build_exe.py"
    - "attendance_system.spec"