"""
语言配置文件
用于管理应用程序的国际化设置
"""
import os
import json
from pathlib import Path
from typing import Dict, Any

class LanguageConfig:
    """语言配置管理类"""
    
    def __init__(self, config_file: str = "language_config.json"):
        self.config_file = Path(config_file)
        self.default_config = {
            "language": "zh_CN",
            "country": "CN",
            "locale": "zh_CN.UTF-8",
            "font_family": "Microsoft YaHei",
            "font_size": 9,
            "date_format": "yyyy-MM-dd",
            "time_format": "HH:mm:ss",
            "datetime_format": "yyyy-MM-dd HH:mm:ss",
            "number_format": {
                "decimal_separator": ".",
                "thousands_separator": ",",
                "decimal_places": 2
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return self.default_config.copy()
        else:
            # 如果配置文件不存在，创建默认配置
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any]) -> None:
        """保存配置文件"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get_language(self) -> str:
        """获取当前语言设置"""
        return self.config.get("language", "zh_CN")
    
    def get_country(self) -> str:
        """获取当前国家设置"""
        return self.config.get("country", "CN")
    
    def get_locale(self) -> str:
        """获取当前区域设置"""
        return self.config.get("locale", "zh_CN.UTF-8")
    
    def get_font_family(self) -> str:
        """获取字体设置"""
        return self.config.get("font_family", "Microsoft YaHei")
    
    def get_font_size(self) -> int:
        """获取字体大小"""
        return self.config.get("font_size", 9)
    
    def get_date_format(self) -> str:
        """获取日期格式"""
        return self.config.get("date_format", "yyyy-MM-dd")
    
    def get_time_format(self) -> str:
        """获取时间格式"""
        return self.config.get("time_format", "HH:mm:ss")
    
    def get_datetime_format(self) -> str:
        """获取日期时间格式"""
        return self.config.get("datetime_format", "yyyy-MM-dd HH:mm:ss")
    
    def set_language(self, language: str, country: str = "CN") -> None:
        """设置语言"""
        self.config["language"] = language
        self.config["country"] = country
        self.config["locale"] = f"{language}_{country}.UTF-8"
        self.save_config(self.config)
    
    def set_font(self, font_family: str, font_size: int = 9) -> None:
        """设置字体"""
        self.config["font_family"] = font_family
        self.config["font_size"] = font_size
        self.save_config(self.config)

# 全局语言配置实例
language_config = LanguageConfig()

def get_language_config() -> LanguageConfig:
    """获取语言配置实例"""
    return language_config

def setup_system_locale():
    """设置系统区域设置"""
    import locale
    try:
        # 设置系统默认语言为中文
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    except locale.Error:
        try:
            # 如果UTF-8不可用，尝试其他编码
            locale.setlocale(locale.LC_ALL, 'zh_CN')
        except locale.Error:
            # 如果都不可用，使用系统默认
            pass

def setup_environment_variables():
    """设置环境变量"""
    # 设置Python环境变量
    os.environ['LANG'] = 'zh_CN.UTF-8'
    os.environ['LC_ALL'] = 'zh_CN.UTF-8'
    os.environ['LC_MESSAGES'] = 'zh_CN.UTF-8'
    
    # 设置时区为中国时区
    os.environ['TZ'] = 'Asia/Shanghai' 