@echo off
chcp 65001 > nul
echo ========================================
echo 考勤系统可执行文件构建脚本
echo ========================================

echo.
echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境
    pause
    exit /b 1
)

echo.
echo 检查虚拟环境...
if exist "venv\Scripts\activate.bat" (
    echo 激活虚拟环境...
    call venv\Scripts\activate.bat
) else (
    echo 警告: 未找到虚拟环境，使用系统Python
)

echo.
echo 检查构建依赖...
python -c "import PyInstaller" 2>nul
if %errorlevel% neq 0 (
    echo 安装PyInstaller...
    pip install pyinstaller==6.3.0
)

echo.
echo 开始构建...
python build_exe.py

if %errorlevel% eq 0 (
    echo.
    echo ========================================
    echo 构建完成!
    echo ========================================
    echo 可执行文件位置: dist\AttendanceSystem.exe
    echo 分发包位置: dist\AttendanceSystem_Package\
    echo ========================================
) else (
    echo.
    echo ========================================
    echo 构建失败!
    echo ========================================
)

echo.
pause
