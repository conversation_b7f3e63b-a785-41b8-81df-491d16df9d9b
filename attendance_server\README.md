# 考勤系统服务端

基于FastAPI和PyQt6开发的考勤系统服务端，支持设备心跳检测、数据同步和实时监控。

## 功能特点

- 设备管理：实时监控设备在线状态，支持心跳检测
- 数据同步：支持从设备端同步考勤数据（kaoqin.db）
- 界面展示：使用PyQt6开发的桌面客户端，支持设备状态显示和同步控制
- API服务：提供RESTful API接口，支持设备接入和数据上传

## 系统要求

- Python 3.11或更高版本
- Windows 10/11操作系统
- Visual C++ Redistributable 2015-2022

## 快速开始

1. 克隆项目到本地：
```bash
git clone <repository_url>
cd attendance_server
```

2. 运行启动脚本：
```bash
scripts\start_server.bat
```
启动脚本会自动：
- 检查Python环境
- 创建虚拟环境
- 安装依赖
- 初始化数据库
- 启动服务

## 目录结构

```
attendance_server/
├── api/                # API接口模块
│   ├── heartbeat.py    # 心跳检测接口
│   └── device.py       # 设备管理接口
├── db/                 # 数据库模块
│   ├── models.py       # 数据模型
│   ├── crud.py        # 数据操作
│   └── database.py    # 数据库连接
├── ui/                 # 界面模块
│   └── main_window.py # 主窗口
├── scripts/           # 脚本文件
│   └── start_server.bat # 启动脚本
├── config.py          # 配置文件
├── main.py           # 主程序
└── requirements.txt   # 依赖列表
```

## API接口

### 心跳检测
- `POST /api/heartbeat/{device_id}`
  - 设备心跳上报
  - 返回同步请求状态

### 数据同步
- `POST /api/heartbeat/upload`
  - 上传考勤数据文件
  - 支持kaoqin.db文件解析

### 设备管理
- `GET /api/devices`
  - 获取所有设备列表
- `GET /api/devices/{device_id}`
  - 获取指定设备状态
- `POST /api/devices/{device_id}/sync`
  - 请求设备同步数据

## 开发说明

1. 安装开发依赖：
```bash
pip install -r requirements.txt
```

2. 运行测试：
```bash
pytest
```

3. 构建可执行文件：
```bash
pyinstaller main.py
```

## 常见问题

1. 如果遇到PyQt6安装失败，请确保已安装Visual C++ Redistributable
2. 如果数据库访问报错，检查数据库文件权限
3. 如果API服务启动失败，检查端口是否被占用

## 许可证

MIT License 