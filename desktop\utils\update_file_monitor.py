#!/usr/bin/env python3
"""
update.txt文件监控模块
监控temp_cfg/update.txt文件的删除状态
"""

import logging
from pathlib import Path
from PyQt6.QtCore import QThread, pyqtSignal, QTimer
from PyQt6.QtWidgets import QApplication


class UpdateFileMonitor(QThread):
    """监控update.txt文件的删除状态"""
    
    # 信号定义
    file_deleted = pyqtSignal()  # 文件被删除信号
    error_occurred = pyqtSignal(str)  # 错误信号
    
    def __init__(self, update_file_path: Path, parent=None):
        super().__init__(parent)
        self.update_file_path = update_file_path
        self.is_monitoring = False
        self.check_interval = 1.0  # 检查间隔（秒）
        self.logger = logging.getLogger(__name__)
        
    def start_monitoring(self):
        """开始监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.start()
            self.logger.info(f"开始监控update.txt文件: {self.update_file_path}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.isRunning():
            self.quit()
            self.wait(3000)  # 等待最多3秒
            self.logger.info("停止监控update.txt文件")
    
    def run(self):
        """监控线程主循环"""
        try:
            while self.is_monitoring:
                try:
                    # 检查文件是否存在
                    if not self.update_file_path.exists():
                        self.logger.info(f"检测到update.txt文件被删除: {self.update_file_path}")
                        self.file_deleted.emit()
                        break  # 文件被删除，停止监控
                    
                    # 等待一段时间再检查
                    self.msleep(int(self.check_interval * 1000))
                    
                except Exception as e:
                    self.error_occurred.emit(f"监控update.txt文件时发生错误: {e}")
                    self.msleep(5000)  # 错误时等待5秒再重试
                    
        except Exception as e:
            self.error_occurred.emit(f"启动update.txt文件监控失败: {e}")
        finally:
            self.is_monitoring = False


class SyncWorkflowController:
    """同步工作流控制器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.update_file_monitor = None
        self.logger = logging.getLogger(__name__)
        
    def start_sync_workflow(self):
        """开始同步工作流"""
        try:
            # 1. 删除temp目录下的所有文件
            self._clear_temp_directory()
            
            # 2. 将按钮文字改为"同步中"
            self.main_window.sync_button.setText("同步中")
            self.main_window.sync_button.setEnabled(False)
            self.main_window.log_viewer.add_log("信息", "同步按钮状态改为'同步中'")
            
            # 3. 创建temp_cfg/update.txt文件
            self._create_update_file()
            
            # 4. 开始监控update.txt文件的删除状态
            self._start_update_file_monitoring()
            
        except Exception as e:
            self.logger.error(f"启动同步工作流失败: {e}")
            self.main_window.log_viewer.add_log("错误", f"启动同步工作流失败: {str(e)}")
            self._reset_sync_state()
    
    def _clear_temp_directory(self):
        """清空temp目录"""
        try:
            from config.path_manager import get_temp_dir
            temp_dir = get_temp_dir()
            
            # 确保temp目录存在
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            # 删除所有文件
            deleted_count = 0
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()
                    deleted_count += 1
            
            self.main_window.log_viewer.add_log("信息", f"已删除temp目录中的 {deleted_count} 个文件")
            self.logger.info(f"清空temp目录完成，删除了 {deleted_count} 个文件")
            
        except Exception as e:
            self.logger.error(f"清空temp目录失败: {e}")
            raise
    
    def _create_update_file(self):
        """创建update.txt文件"""
        try:
            from config.path_manager import get_temp_cfg_dir
            temp_cfg_dir = get_temp_cfg_dir()
            temp_cfg_dir.mkdir(parents=True, exist_ok=True)
            
            update_file = temp_cfg_dir / "update.txt"
            update_file.touch()
            
            self.main_window.log_viewer.add_log("信息", "已创建update.txt文件，等待设备响应...")
            self.logger.info(f"创建update.txt文件: {update_file}")
            
        except Exception as e:
            self.logger.error(f"创建update.txt文件失败: {e}")
            raise
    
    def _start_update_file_monitoring(self):
        """开始监控update.txt文件"""
        try:
            from config.path_manager import get_temp_cfg_dir
            temp_cfg_dir = get_temp_cfg_dir()
            update_file = temp_cfg_dir / "update.txt"
            
            # 创建监控器
            self.update_file_monitor = UpdateFileMonitor(update_file)
            
            # 连接信号
            self.update_file_monitor.file_deleted.connect(self._on_update_file_deleted)
            self.update_file_monitor.error_occurred.connect(self._on_monitor_error)
            
            # 开始监控
            self.update_file_monitor.start_monitoring()
            
            self.main_window.log_viewer.add_log("信息", "开始监控update.txt文件删除状态")
            self.logger.info("开始监控update.txt文件删除状态")
            
        except Exception as e:
            self.logger.error(f"启动update.txt文件监控失败: {e}")
            raise
    
    def _on_update_file_deleted(self):
        """当update.txt文件被删除时的处理"""
        try:
            self.logger.info("检测到update.txt文件被删除，开始后续处理")
            self.main_window.log_viewer.add_log("信息", "检测到update.txt文件被删除，开始处理同步完成流程")
            
            # 1. 恢复按钮可点击状态
            self.main_window.sync_button.setEnabled(True)
            self.main_window.sync_button.setText("同步")
            self.main_window.log_viewer.add_log("信息", "同步按钮状态已恢复")
            
            # 2. 将temp目录下的所有文件移动到temp_bak目录
            self._move_files_to_backup()
            
            # 3. 显示temp_bak目录中所有*.db文件的内容到考勤记录界面
            self._display_attendance_records()
            
            # 4. 停止监控
            if self.update_file_monitor:
                self.update_file_monitor.stop_monitoring()
                self.update_file_monitor = None
            
        except Exception as e:
            self.logger.error(f"处理update.txt文件删除事件失败: {e}")
            self.main_window.log_viewer.add_log("错误", f"处理同步完成流程失败: {str(e)}")
            self._reset_sync_state()
    
    def _move_files_to_backup(self):
        """将temp目录文件移动到temp_bak目录"""
        try:
            from config.path_manager import get_temp_dir, get_temp_bak_dir
            import shutil
            
            temp_dir = get_temp_dir()
            temp_bak_dir = get_temp_bak_dir()
            
            # 确保temp_bak目录存在
            temp_bak_dir.mkdir(parents=True, exist_ok=True)
            
            # 移动所有文件
            moved_count = 0
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    dest_path = temp_bak_dir / file_path.name
                    
                    # 如果目标文件存在则覆盖
                    if dest_path.exists():
                        dest_path.unlink()
                    
                    # 移动文件
                    shutil.move(str(file_path), str(dest_path))
                    moved_count += 1
                    self.logger.info(f"移动文件: {file_path.name} -> temp_bak/")
            
            self.main_window.log_viewer.add_log("信息", f"已将 {moved_count} 个文件移动到temp_bak目录")
            self.logger.info(f"文件移动完成，共移动 {moved_count} 个文件")
            
        except Exception as e:
            self.logger.error(f"移动文件到temp_bak失败: {e}")
            raise
    
    def _display_attendance_records(self):
        """显示考勤记录"""
        try:
            # 使用现有的load_temp_bak_records方法显示最新100条记录
            self.main_window.attendance_table.load_temp_bak_records()
            
            # 统计显示的记录数
            record_count = self.main_window.attendance_table.rowCount()
            self.main_window.log_viewer.add_log("信息", f"已显示 {record_count} 条考勤记录（最新100条）")
            self.logger.info(f"显示考勤记录完成，共 {record_count} 条")
            
        except Exception as e:
            self.logger.error(f"显示考勤记录失败: {e}")
            raise
    
    def _on_monitor_error(self, error_message):
        """监控错误处理"""
        self.logger.error(f"update.txt文件监控错误: {error_message}")
        self.main_window.log_viewer.add_log("错误", f"文件监控错误: {error_message}")
        self._reset_sync_state()
    
    def _reset_sync_state(self):
        """重置同步状态"""
        try:
            # 恢复按钮状态
            self.main_window.sync_button.setEnabled(True)
            self.main_window.sync_button.setText("同步")
            
            # 停止监控
            if self.update_file_monitor:
                self.update_file_monitor.stop_monitoring()
                self.update_file_monitor = None
            
            self.logger.info("同步状态已重置")
            
        except Exception as e:
            self.logger.error(f"重置同步状态失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        if self.update_file_monitor:
            self.update_file_monitor.stop_monitoring()
            self.update_file_monitor = None
