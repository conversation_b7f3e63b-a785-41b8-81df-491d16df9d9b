#!/usr/bin/env python3
"""
考勤系统可执行文件构建脚本
自动化打包流程，包括环境检查、依赖安装、打包和验证
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path
import argparse

class AttendanceSystemBuilder:
    """考勤系统构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "attendance_system.spec"
        
    def log(self, message: str, level: str = "INFO"):
        """打印日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def run_command(self, cmd: list, cwd: Path = None, check: bool = True) -> subprocess.CompletedProcess:
        """运行命令并返回结果"""
        if cwd is None:
            cwd = self.project_root
            
        self.log(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                cwd=cwd,
                capture_output=True,
                text=True,
                check=check
            )
            
            if result.stdout:
                self.log(f"输出: {result.stdout.strip()}")
            if result.stderr:
                self.log(f"错误: {result.stderr.strip()}", "WARNING")
                
            return result
            
        except subprocess.CalledProcessError as e:
            self.log(f"命令执行失败: {e}", "ERROR")
            if e.stdout:
                self.log(f"标准输出: {e.stdout}", "ERROR")
            if e.stderr:
                self.log(f"错误输出: {e.stderr}", "ERROR")
            raise
    
    def check_python_version(self):
        """检查Python版本"""
        self.log("检查Python版本...")
        
        if sys.version_info < (3, 8):
            raise RuntimeError(f"需要Python 3.8或更高版本，当前版本: {sys.version}")
        
        self.log(f"Python版本检查通过: {sys.version}")
    
    def install_build_dependencies(self):
        """安装构建依赖"""
        self.log("安装构建依赖...")
        
        # 检查是否存在虚拟环境
        venv_path = self.project_root / "venv"
        if venv_path.exists():
            self.log("检测到虚拟环境，将在虚拟环境中安装依赖")
            if sys.platform == "win32":
                python_exe = venv_path / "Scripts" / "python.exe"
                pip_exe = venv_path / "Scripts" / "pip.exe"
            else:
                python_exe = venv_path / "bin" / "python"
                pip_exe = venv_path / "bin" / "pip"
        else:
            python_exe = sys.executable
            pip_exe = "pip"
        
        # 升级pip
        self.run_command([str(python_exe), "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装构建依赖
        build_requirements = self.project_root / "build_requirements.txt"
        if build_requirements.exists():
            self.run_command([str(pip_exe), "install", "-r", str(build_requirements)])
        else:
            # 如果没有build_requirements.txt，安装基本依赖
            self.run_command([str(pip_exe), "install", "pyinstaller==6.3.0"])
            self.run_command([str(pip_exe), "install", "-r", "requirements.txt"])
    
    def clean_build_directories(self):
        """清理构建目录"""
        self.log("清理构建目录...")
        
        for directory in [self.dist_dir, self.build_dir]:
            if directory.exists():
                self.log(f"删除目录: {directory}")
                shutil.rmtree(directory)
    
    def create_necessary_directories(self):
        """创建必要的目录"""
        self.log("创建必要的目录...")
        
        directories = ["temp", "temp_bak", "temp_cfg", "attendance_server/logs"]
        
        for dir_name in directories:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                self.log(f"创建目录: {dir_path}")
                
                # 创建.gitkeep文件以确保目录被包含
                gitkeep_file = dir_path / ".gitkeep"
                gitkeep_file.touch()
    
    def build_executable(self):
        """构建可执行文件"""
        self.log("开始构建可执行文件...")
        
        if not self.spec_file.exists():
            raise FileNotFoundError(f"规格文件不存在: {self.spec_file}")
        
        # 使用PyInstaller构建
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",  # 清理临时文件
            "--noconfirm",  # 不询问确认
            str(self.spec_file)
        ]
        
        self.run_command(cmd)
        
        # 检查构建结果
        exe_file = self.dist_dir / "AttendanceSystem.exe"
        if exe_file.exists():
            file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
            self.log(f"构建成功! 可执行文件: {exe_file}")
            self.log(f"文件大小: {file_size:.2f} MB")
            return exe_file
        else:
            raise RuntimeError("构建失败，未找到可执行文件")
    
    def create_distribution_package(self, exe_file: Path):
        """创建分发包"""
        self.log("创建分发包...")
        
        # 创建分发目录
        package_dir = self.dist_dir / "AttendanceSystem_Package"
        package_dir.mkdir(exist_ok=True)
        
        # 复制可执行文件
        shutil.copy2(exe_file, package_dir / "AttendanceSystem.exe")
        
        # 创建使用说明
        readme_content = """# 考勤系统使用说明

## 系统要求
- Windows 10 或更高版本
- 无需安装Python环境

## 使用方法
1. 双击 AttendanceSystem.exe 启动程序
2. 系统会自动启动服务端和桌面程序
3. 桌面程序关闭时，服务端也会自动关闭

## 功能特性
- 考勤记录管理
- UDP广播服务发现
- 数据备份和恢复
- Excel数据导出

## 网络配置
- HTTP服务端口: 8000
- UDP广播端口: 37020
- 确保防火墙允许这些端口的通信

## 故障排除
1. 如果程序无法启动，请检查是否有杀毒软件阻止
2. 如果网络功能异常，请检查防火墙设置
3. 程序运行时会在控制台显示详细日志信息

## 技术支持
如有问题，请查看控制台输出的错误信息。
"""
        
        readme_file = package_dir / "使用说明.txt"
        readme_file.write_text(readme_content, encoding='utf-8')
        
        self.log(f"分发包创建完成: {package_dir}")
        return package_dir
    
    def verify_executable(self, exe_file: Path):
        """验证可执行文件"""
        self.log("验证可执行文件...")
        
        if not exe_file.exists():
            raise FileNotFoundError(f"可执行文件不存在: {exe_file}")
        
        # 检查文件大小
        file_size = exe_file.stat().st_size
        if file_size < 1024 * 1024:  # 小于1MB可能有问题
            self.log(f"警告: 可执行文件大小异常小: {file_size} bytes", "WARNING")
        
        # 尝试运行帮助命令（快速验证）
        try:
            result = subprocess.run(
                [str(exe_file), "--help"],
                capture_output=True,
                text=True,
                timeout=30
            )
            if result.returncode == 0:
                self.log("可执行文件验证通过")
            else:
                self.log(f"可执行文件验证失败，返回码: {result.returncode}", "WARNING")
        except subprocess.TimeoutExpired:
            self.log("可执行文件验证超时", "WARNING")
        except Exception as e:
            self.log(f"可执行文件验证出错: {e}", "WARNING")
    
    def build(self, clean: bool = True, verify: bool = True):
        """执行完整的构建流程"""
        try:
            self.log("开始构建考勤系统可执行文件")
            
            # 1. 检查环境
            self.check_python_version()
            
            # 2. 安装依赖
            self.install_build_dependencies()
            
            # 3. 清理构建目录
            if clean:
                self.clean_build_directories()
            
            # 4. 创建必要目录
            self.create_necessary_directories()
            
            # 5. 构建可执行文件
            exe_file = self.build_executable()
            
            # 6. 验证可执行文件
            if verify:
                self.verify_executable(exe_file)
            
            # 7. 创建分发包
            package_dir = self.create_distribution_package(exe_file)
            
            self.log("构建完成!")
            self.log(f"可执行文件: {exe_file}")
            self.log(f"分发包: {package_dir}")
            
            return exe_file, package_dir
            
        except Exception as e:
            self.log(f"构建失败: {e}", "ERROR")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="考勤系统可执行文件构建工具")
    parser.add_argument("--no-clean", action="store_true", help="不清理构建目录")
    parser.add_argument("--no-verify", action="store_true", help="不验证可执行文件")
    
    args = parser.parse_args()
    
    builder = AttendanceSystemBuilder()
    
    try:
        exe_file, package_dir = builder.build(
            clean=not args.no_clean,
            verify=not args.no_verify
        )
        
        print("\n" + "="*60)
        print("构建成功!")
        print(f"可执行文件: {exe_file}")
        print(f"分发包目录: {package_dir}")
        print("="*60)
        
    except Exception as e:
        print(f"\n构建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
